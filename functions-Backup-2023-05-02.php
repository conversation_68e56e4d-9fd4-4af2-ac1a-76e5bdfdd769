<?php 

// require_once dirname(__FILE__) . '../../vendor/autoload.php';
// $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
// $dotenv->load();

add_action( 'wp_enqueue_scripts', 'salient_child_enqueue_styles', 100);

function salient_child_enqueue_styles() {
		
	$nectar_theme_version = nectar_get_theme_version();
	wp_enqueue_style( 'salient-child-style', get_stylesheet_directory_uri() . '/style.css', '', $nectar_theme_version );
		
    if ( is_rtl() ) {
		wp_enqueue_style(  'salient-rtl',  get_template_directory_uri(). '/rtl.css', array(), '1', 'screen' );
	}
}

// Require new custom Element
// require_once( get_template_directory().'/vc-components/vc-soda-blockquote.php' );


/**
 * Adds new shortcode "myprefix_say_hello" and registers it to
 * the Visual Composer plugin
 *
 */
if ( ! class_exists( 'MyPrefix_Say_Hello_Shortcode' ) ) {

    class MyPrefix_Say_Hello_Shortcode {

        /**
         * Main constructor
         */
        public function __construct() {

            // Registers the shortcode in WordPress
            add_shortcode( 'myprefix_say_hello', __CLASS__ . '::output' );

            // Map shortcode to WPBakery so you can access it in the builder
            if ( function_exists( 'vc_lean_map' ) ) {
                vc_lean_map( 'myprefix_say_hello', __CLASS__ . '::map' );
            }

        }

        /**
         * Shortcode output
         */
        public static function output( $atts, $content = null ) {

            // Extract shortcode attributes (based on the vc_lean_map function - see next function)
            $atts = vc_map_get_attributes( 'myprefix_say_hello', $atts );
            // $atts = vc_map_get_attributes(array( 
			// 	'myprefix_say_hello',
			// 	'myprefix_say_hello_image'
			// ), $atts );
			

            // Define output and open element div.
            $output = '<div class="my-hello-element">';

                // Display custom heading if enabled and set.
                if ( isset( $atts['show_heading'] )
                    && 'yes' === $atts['show_heading']
                    && ! empty( $atts['heading'] )
                ) {
                    $output .= '<h2 class="my-hello-element__heading">' . esc_html( $atts['heading'] ) . '</h2>';
                }

                // Display content.
                $output .= '<div class="my-hello-element__content">';
                    if ( $content ) {
                        $output .= wp_kses_post( $content );
                    } else {
                        $output .= 'Hello';
                    }
                $output .= '</div>';

            // Close element.
            $output .= '</div>';

            // Return output
            return $output;

        }

        /**
         * Map shortcode to WPBakery
         *
         * This is an array of all your settings which become the shortcode attributes ($atts)
         * for the output. See the link below for a description of all available parameters.
         *
         * @since 1.0.0
         * @link  https://kb.wpbakery.com/docs/inner-api/vc_map/
         */
        public static function map() {
            return array(
                'name'        => esc_html__( 'Say Hello', 'locale' ),
                'description' => esc_html__( 'Shortcode outputs Hello.', 'locale' ),
                'base'        => 'myprefix_say_hello',
                'params'      => array(
                    array(
                        'type'       => 'dropdown',
                        'heading'    => esc_html__( 'Show Heading?', 'locale' ),
                        'param_name' => 'show_heading',
                        'value'      => array(
                            esc_html__( 'No', 'locale' )  => 'no',
                            esc_html__( 'Yes', 'locale' ) => 'yes',
                        ),
                    ),
                    array(
                        'type'       => 'textfield',
                        'heading'    => esc_html__( 'Heading', 'locale' ),
                        'param_name' => 'heading',
                        'dependency' => array( 'element' => 'show_heading', 'value' => 'yes' ),
                    ),
                    array(
                        'type'       => 'textarea_html',
                        'heading'    => esc_html__( 'Custom Text', 'locale' ),
                        'param_name' => 'content',
                    ),
                ),
            );
        }

    }

}
new MyPrefix_Say_Hello_Shortcode;




/**
 * Adds new shortcode "Advanced Hero Section" and registers it to
 * the Visual Composer plugin
 *
 */
if ( ! class_exists( 'Advanced_Hero_Section_Shortcode' ) ) {

    class Advanced_Hero_Section_Shortcode {
    // class Advanced_Hero_Section_Shortcode extends WPBakeryShortCode {

        /**
         * Main constructor
         */
        public function __construct() {

            // Registers the shortcode in WordPress
            add_shortcode( 'myprefix_advanced_hero_element', __CLASS__ . '::output' );

            // Map shortcode to WPBakery so you can access it in the builder
            if ( function_exists( 'vc_lean_map' ) ) {
                vc_lean_map( 'myprefix_advanced_hero_element', __CLASS__ . '::map' );
            }

        }

        /**
         * Shortcode output
         */
        public static function output( $atts, $content, $image = null ) {

            // Extract shortcode attributes (based on the vc_lean_map function - see next function)
            $atts = vc_map_get_attributes( 'myprefix_advanced_hero_element', $atts );

            // Define output and open element div.
            $output = '<div class="advanced-hero-element">';

				// Display content.
				$output .= '<div class="advanced-hero-element__image">';
				// $output .= wp_kses_post( $hero_image );
				// $output .= '<img src="' . esc_html([$atts'] ) . '';
				// $output .= '<img src="' . esc_html( $atts['image'] ) . '>';
				$output .= '<p class="advanced-hero-element__heading"> 
								<img src="' . wp_get_attachment_image_url( $atts['image'] ) . '">' . esc_html( $atts['image'] ) . 
							'</p>';
				$output .= '</div>';

                // Display custom heading if enabled and set.
                if ( isset( $atts['show_heading'] )
                    && 'yes' === $atts['show_heading']
                    && ! empty( $atts['heading'] )
                ) {
                    $output .= '<h2 class="advanced-hero-element__heading">' . esc_html( $atts['heading'] ) . '</h2>';
                }

				// Display Subheading
				if ( isset( $atts['show_heading'] )
                    && 'yes' === $atts['show_heading']
                    && ! empty( $atts['subheading'] )
                ) {
                    $output .= '<h3 class="advanced-hero-element__heading">' . esc_html( $atts['subheading'] ) . '</h3>';
                }

                // Display content.
                $output .= '<div class="advanced-hero-element__content">';
                    if ( $content ) {
                        $output .= wp_kses_post( $content );
                    } else {
                        $output .= 'Hello';
                    }
                $output .= '</div>';

            // Close element.
            $output .= '</div>';

            // Return output
            return $output;

        }

        /**
         * Map shortcode to WPBakery
         *
         * This is an array of all your settings which become the shortcode attributes ($atts)
         * for the output. See the link below for a description of all available parameters.
         *
         * @since 1.0.0
         * @link  https://kb.wpbakery.com/docs/inner-api/vc_map/
         */
        public static function map() {
            return array(
                'name'        => esc_html__( 'Advanced Hero Section', 'locale' ),
                'description' => esc_html__( 'Shortcode outputs Hero Section.', 'locale' ),
                'base'        => 'myprefix_advanced_hero_element',
                'params'      => array(
					array(
						"type" => "attach_image",
						// "class" => "",
						"heading" => __( "Image", "locale" ),
						"param_name" => "image",
						"value" => 'url',
						"description" => __( "Enter description.", "my-text-domain" )
					),
                    array(
                        'type'       => 'dropdown',
                        'heading'    => esc_html__( 'Show Heading?', 'locale' ),
                        'param_name' => 'show_heading',
                        'value'      => array(
                            esc_html__( 'No', 'locale' )  => 'no',
                            esc_html__( 'Yes', 'locale' ) => 'yes',
                        ),
                    ),
                    array(
                        'type'       => 'textfield',
                        'heading'    => esc_html__( 'Heading', 'locale' ),
                        'param_name' => 'heading',
                        'dependency' => array( 'element' => 'show_heading', 'value' => 'yes' ),
                    ),
					array(
                        'type'       => 'textfield',
                        'heading'    => esc_html__( 'Subheading', 'locale' ),
                        'param_name' => 'subheading',
                        'dependency' => array( 'element' => 'show_heading', 'value' => 'yes' ),
                    ),
                    array(
                        'type'       => 'textarea_html',
                        'heading'    => esc_html__( 'Custom Text', 'locale' ),
                        'param_name' => 'content',
                    ),
                ),
            );
        }

    }

}
new Advanced_Hero_Section_Shortcode;






/*
/* !!! Define Berding Hero Element !!!
/* Description: Lorem ipsum  
*/
if ( ! class_exists( 'VcSodaBlockquote' ) ) {
	
	class VcSodaBlockquote extends WPBakeryShortCode {

		function __construct() {
			add_action( 'init', array( $this, 'create_shortcode' ), 999 );
			add_shortcode( 'vc_soda_blockquote', array( $this, 'render_shortcode' ) );
	
		}
	
		public function create_shortcode() {
			// Stop all if VC is not enabled
			if ( !defined( 'WPB_VC_VERSION' ) ) {
				return;
			}
	
			// Map blockquote with vc_map()
			vc_map( array(
				'name'          => __('Berding Hero Section', 'rr'),
				'base'          => 'vc_soda_blockquote',
				'description'  	=> __( '', 'rr' ),
				'category'      => __( 'rr Modules', 'rr'),
				'params' => array(
					
					

					array(
						"type" => "textarea_html",
						"holder" => "div",
						"class" => "",
						"heading" => __( "Content 1", 'rr' ),
						"param_name" => "content", // Important: Only one textarea_html param per content element allowed and it should have "content" as a "param_name"
						"value" => __( "<p>I am test text block. Click edit button to change this text.</p>", 'rr' ),
						"description" => __( "Content für erste Ansicht.", 'rr' )
					),

					/* ////////// */ 
					/* Mask Image */ 
					/* ////////// */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Mask Image URL", "rr" ),
						"param_name" => "mask_image_url",
						"value" => '',
						"description" => __( "Mask Image (wird benutzt, um das Hintergrundbild zu maskieren) – Wichtig: Benutze ein SVG!", "rr" )
					),

					/* ////// */ 
					/* View 1 */
					/* ////// */
					
					/* Name of Views */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'View Name', 'rr' ),
						'param_name'    => 'view_name_one',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Headline für erste Ansicht (z.B. "Produktion" oder "Technik")', 'rr' ),
						'group'         => __( 'View 1', 'rr'),
					),
					/* H1 */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Hedline', 'rr' ),
						'param_name'    => 'heading_one',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Headline (h1) für erste Ansicht (z.B. "Maschinenführer und Deichbeschützer")', 'rr' ),
						'group'         => __( 'View 1', 'rr'),
					),

					/* Background Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Image URL", "rr" ),
						"param_name" => "image_url",
						"value" => '',
						"description" => __( "Enter description.", "my-text-domain" ),
						'group'         => __( 'View 1', 'rr'),
					),
					/* Testimonial Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Testimonial Image URL", "rr" ),
						"param_name" => "testimonial_image_url",
						"value" => '',
						"description" => __( "Testimonial Image", "rr" ),
						'group'         => __( 'View 1', 'rr'),
					),
					/* Testimonial position */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Testimonial Position', 'rr' ),
						'param_name'    => 'testimonial_position_one',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Testimonial Position (include unit e.g. px, vw, etc.)', 'rr' ),
						'group'         => __( 'View 1', 'rr'),
					),

					/* Multiple Images for flying stuff */ 
					array(
						"type" => "attach_images",
						"class" => "",
						"heading" => __( "Multiple Image URLs", "rr" ),
						"param_name" => "multiple_images",
						"value" => '',
						"description" => __( "Multiple Images für fligende Effekte (maximal 3 Bilder – alle weiteren werden nicht berücksichtigt).", "rr" ),
						'group'         => __( 'View 1', 'rr'),
					),

					/* Button */ 
					array(
						"type" => "vc_link",
						"class" => "",
						"heading" => __( "Button", 'rr' ),
						"param_name" => "button_one",
						"description" => __( "Add Button Text and Link", 'rr' ),
						'group'         => __( 'View 1', 'rr'),
					),


					/* ////// */ 
					/* View 2 */
					/* ////// */
					
					/* Name of Views */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'View Name', 'rr' ),
						'param_name'    => 'view_name_two',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Headline für erste Ansicht (z.B. "Produktion" oder "Technik")', 'rr' ),
						'group'         => __( 'View 2', 'rr'),
					),

					/* H1 */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Hedline', 'rr' ),
						'param_name'    => 'heading_two',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Headline (h1) für erste Ansicht (z.B. "Maschinenführer und Deichbeschützer")', 'rr' ),
						'group'         => __( 'View 2', 'rr'),
					),

					/* Background Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Image URL", "rr" ),
						"param_name" => "image_url_two",
						"value" => '',
						"description" => __( "Enter description.", "my-text-domain" ),
						'group'         => __( 'View 2', 'rr'),
					),
					/* Testimonial Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Testimonial Image URL", "rr" ),
						"param_name" => "testimonial_image_url_two",
						"value" => '',
						"description" => __( "Testimonial Image", "rr" ),
						'group'         => __( 'View 2', 'rr'),
					),
					/* Testimonial position */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Testimonial Position', 'rr' ),
						'param_name'    => 'testimonial_position_two',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Testimonial Position (include unit e.g. px, vw, etc.)', 'rr' ),
						'group'         => __( 'View 1', 'rr'),
					),

					/* Multiple Images for flying stuff */ 
					array(
						"type" => "attach_images",
						"class" => "",
						"heading" => __( "Multiple Image URLs", "rr" ),
						"param_name" => "multiple_images_two",
						"value" => '',
						"description" => __( "Multiple Images für fligende Effekte (maximal 3 Bilder – alle weiteren werden nicht berücksichtigt).", "rr" ),
						'group'         => __( 'View 2', 'rr'),
					),

					/* Button */ 
					array(
						"type" => "vc_link",
						"class" => "",
						"heading" => __( "Button", 'rr' ),
						"param_name" => "button_two",
						"description" => __( "Add Button Text and Link", 'rr' ),
						'group'         => __( 'View 2', 'rr'),
					),



					/* ////// */ 
					/* View 3 */
					/* ////// */
					
					/* Name of Views */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'View Name', 'rr' ),
						'param_name'    => 'view_name_three',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Headline für erste Ansicht (z.B. "Produktion" oder "Technik")', 'rr' ),
						'group'         => __( 'View 3', 'rr'),
					),

					/* H1 */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Hedline', 'rr' ),
						'param_name'    => 'heading_three',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Headline (h1) für erste Ansicht (z.B. "Maschinenführer und Deichbeschützer")', 'rr' ),
						'group'         => __( 'View 3', 'rr'),
					),

					/* Background Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Image URL", "rr" ),
						"param_name" => "image_url_three",
						"value" => '',
						"description" => __( "Enter description.", "my-text-domain" ),
						'group'         => __( 'View 3', 'rr'),
					),
					/* Testimonial Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Testimonial Image URL", "rr" ),
						"param_name" => "testimonial_image_url_three",
						"value" => '',
						"description" => __( "Testimonial Image", "rr" ),
						'group'         => __( 'View 3', 'rr'),
					),
					/* Testimonial position */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Testimonial Position', 'rr' ),
						'param_name'    => 'testimonial_position_three',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Testimonial Position (include unit e.g. px, vw, etc.)', 'rr' ),
						'group'         => __( 'View 1', 'rr'),
					),

					/* Multiple Images for flying stuff */ 
					array(
						"type" => "attach_images",
						"class" => "",
						"heading" => __( "Multiple Image URLs", "rr" ),
						"param_name" => "multiple_images_three",
						"value" => '',
						"description" => __( "Multiple Images für fligende Effekte (maximal 3 Bilder – alle weiteren werden nicht berücksichtigt).", "rr" ),
						'group'         => __( 'View 3', 'rr'),
					),

					/* Button */ 
					array(
						"type" => "vc_link",
						"class" => "",
						"heading" => __( "Button", 'rr' ),
						"param_name" => "button_three",
						"description" => __( "Add Button Text and Link", 'rr' ),
						'group'         => __( 'View 3', 'rr'),
					),



					/* ////// */ 
					/* View 4 */
					/* ////// */
					
					/* Name of Views */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'View Name', 'rr' ),
						'param_name'    => 'view_name_four',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Headline für erste Ansicht (z.B. "Produktion" oder "Technik")', 'rr' ),
						'group'         => __( 'View 4', 'rr'),
					),

					/* H1 */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Hedline', 'rr' ),
						'param_name'    => 'heading_four',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Headline (h1) für erste Ansicht (z.B. "Maschinenführer und Deichbeschützer")', 'rr' ),
						'group'         => __( 'View 4', 'rr'),
					),

					/* Background Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Image URL", "rr" ),
						"param_name" => "image_url_four",
						"value" => '',
						"description" => __( "Enter description.", "my-text-domain" ),
						'group'         => __( 'View 4', 'rr'),
					),
					/* Testimonial Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Testimonial Image URL", "rr" ),
						"param_name" => "testimonial_image_url_four",
						"value" => '',
						"description" => __( "Testimonial Image", "rr" ),
						'group'         => __( 'View 4', 'rr'),
					),
					/* Testimonial position */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Testimonial Position', 'rr' ),
						'param_name'    => 'testimonial_position_four',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Testimonial Position (include unit e.g. px, vw, etc.)', 'rr' ),
						'group'         => __( 'View 1', 'rr'),
					),

					/* Multiple Images for flying stuff */ 
					array(
						"type" => "attach_images",
						"class" => "",
						"heading" => __( "Multiple Image URLs", "rr" ),
						"param_name" => "multiple_images_four",
						"value" => '',
						"description" => __( "Multiple Images für fligende Effekte (maximal 3 Bilder – alle weiteren werden nicht berücksichtigt).", "rr" ),
						'group'         => __( 'View 4', 'rr'),
					),

					/* Button */ 
					array(
						"type" => "vc_link",
						"class" => "",
						"heading" => __( "Button", 'rr' ),
						"param_name" => "button_four",
						"description" => __( "Add Button Text and Link", 'rr' ),
						'group'         => __( 'View 4', 'rr'),
					),

	
					
					/* //////////// */ 
					/* GLOBAL STUFF */ 
					/* //////////// */ 
					array(
						'type'          => 'textfield',
						'heading'       => __( 'Element ID', 'rr' ),
						'param_name'    => 'element_id',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Enter element ID (Note: make sure it is unique and valid).', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),
	
					array(
						'type'          => 'textfield',
						'heading'       => __( 'Extra class name', 'rr' ),
						'param_name'    => 'extra_class',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Style particular content element differently - add a class name and refer to it in custom CSS.', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),
				),
			));
	
		}
	
		public function render_shortcode( $atts, $content, $tag ) {
			$atts = (shortcode_atts(array(
				// Allgemeine Einstellungen 
				'mask_image_url' 			=> '',
				
				// View One 
				'view_name_one'      		=> '',
				'heading_one'      			=> '',
				'image_url' 				=> '',
				'testimonial_image_url' 	=> '',
				'testimonial_position_one' => '', 
				'multiple_images'			=> '',
				'button_one'   				=> '',

				// View Two 
				'view_name_two'      			=> '',
				'heading_two'      				=> '',
				'image_url_two' 				=> '',
				'testimonial_image_url_two' 	=> '',
				'testimonial_position_two' 	=> '', 
				'multiple_images_two'			=> '',
				'button_two'	   				=> '',

				// View Three 
				'view_name_three'      			=> '',
				'heading_three'      			=> '',
				'image_url_three' 				=> '',
				'testimonial_image_url_three' 	=> '',
				'testimonial_position_three' 	=> '', 
				'multiple_images_three'			=> '',
				'button_three'	   				=> '',

				// View Four 
				'view_name_four'      			=> '',
				'heading_four'      			=> '',
				'image_url_four' 				=> '',
				'testimonial_image_url_four' 	=> '',
				'testimonial_position_four' 	=> '', 
				'multiple_images_four'			=> '',
				'button_four'	   				=> '',

				// Extra Einstellungen 
				'extra_class'       		=> '',
				// 'element_id'        		=> ''
			), $atts));
	
			// Console.log Function
			/*
			function debug_to_console($data) {
				$output = $data;
				if (is_array($output))
					$output = implode(',', $output);
			
				echo "<script>console.log('Debug Objects: " . $output . "' );</script>";
			}
			*/

			/* ////// */ 
			/* GLOBAL */ 
			/* ////// */

			$content            = wpb_js_remove_wpautop($content, true);

			$mask_image_url 	= esc_html($atts['mask_image_url']);
			
			/* ////// */ 
			/* VIEW 1 */ 
			/* ////// */ 
			// Content
			
			$view_name_one       = esc_html($atts['view_name_one']);
			$heading_one       = esc_html($atts['heading_one']);
			
			// Background Image 
			$image_url       	= esc_html($atts['image_url']);

			// Testimonal Image 
			$testimonial_image_url 	= esc_html($atts['testimonial_image_url']);
			// Testimonial Position
			$testimonial_position_one       = esc_html($atts['testimonial_position_one']);
			
			// Multiple Images 
			$multiple_images 	= esc_html($atts['multiple_images']);

			$first_multiple_image = substr($multiple_images, 0, strpos($multiple_images, ','));
			$second_multiple_image = substr($multiple_images, strpos($multiple_images, ',') + 1, 99);

			// Button 
			// $button_text       	= esc_html($atts['button_text']);
			// $button_source 		= vc_build_link( $atts['button_data'] );
			// $button_title 		= esc_html($button_source["title"]);
			// $button_url     	= esc_url( $button_source['url'] ); 
	
			// Button
			$button_one_source  = vc_build_link( $atts['button_one'] );
			$button_one_title   = esc_html($button_one_source["title"]);
			$button_one_url     = esc_url( $button_one_source['url'] );

			/* ////// */ 
			/* VIEW 2 */ 
			/* ////// */ 
			$view_name_two       = esc_html($atts['view_name_two']);
			$heading_two       = esc_html($atts['heading_two']);
			
			// Image 
			$image_url_two       	= esc_html($atts['image_url_two']);

			$testimonial_image_url_two 	= esc_html($atts['testimonial_image_url_two']);
			// Testimonial Position
			$testimonial_position_two       = esc_html($atts['testimonial_position_two']);
			
			// Multiple Images 
			$multiple_images_two 	= esc_html($atts['multiple_images_two']);

			$first_multiple_image_two = substr($multiple_images_two, 0, strpos($multiple_images_two, ','));
			$second_multiple_image_two = substr($multiple_images_two, strpos($multiple_images_two, ',') + 1, 99);
			$third_multiple_image_two = substr($multiple_images_two, strpos($multiple_images_two, ',') -1, 99);

			$myThirdMultipleImageArray = explode(',', $multiple_images_two);


			// Button
			$button_two_source  = vc_build_link( $atts['button_two'] );
			$button_two_title   = esc_html($button_two_source["title"]);
			$button_two_url     = esc_url( $button_two_source['url'] );


			/* ////// */ 
			/* VIEW 3 */ 
			/* ////// */ 
			$view_name_three       = esc_html($atts['view_name_three']);
			$heading_three       = esc_html($atts['heading_three']);
			
			// Image 
			$image_url_three       	= esc_html($atts['image_url_three']);

			$testimonial_image_url_three 	= esc_html($atts['testimonial_image_url_three']);
			// Testimonial Position
			$testimonial_position_three       = esc_html($atts['testimonial_position_three']);
			
			// Multiple Images 
			$multiple_images_three 	= esc_html($atts['multiple_images_three']);

			$first_multiple_image_three = substr($multiple_images_three, 0, strpos($multiple_images_three, ','));
			$second_multiple_image_three = substr($multiple_images_three, strpos($multiple_images_three, ',') + 1, 99);

			// Button
			$button_three_source  = vc_build_link( $atts['button_three'] );
			$button_three_title   = esc_html($button_three_source["title"]);
			$button_three_url     = esc_url( $button_three_source['url'] );



			/* ////// */ 
			/* VIEW 4 */ 
			/* ////// */ 
			$view_name_four       = esc_html($atts['view_name_four']);
			$heading_four       = esc_html($atts['heading_four']);
			
			// Image 
			$image_url_four       	= esc_html($atts['image_url_four']);

			$testimonial_image_url_four 	= esc_html($atts['testimonial_image_url_four']);
			// Testimonial Position
			$testimonial_position_four       = esc_html($atts['testimonial_position_four']);
			
			// Multiple Images 
			$multiple_images_four 	= esc_html($atts['multiple_images_four']);

			$first_multiple_image_four = substr($multiple_images_four, 0, strpos($multiple_images_four, ','));
			$second_multiple_image_four = substr($multiple_images_four, strpos($multiple_images_four, ',') + 1, 99);

			// Button
			$button_four_source  = vc_build_link( $atts['button_four'] );
			$button_four_title   = esc_html($button_four_source["title"]);
			$button_four_url     = esc_url( $button_four_source['url'] );


			/* ////// */
			/* GLOBAL */
			/* ////// */

			//Class and Id
			$extra_class        = esc_attr($atts['extra_class']);
			// $element_id         = esc_attr($atts['element_id']);
	
			/*
			debug_to_console($multiple_images);
			debug_to_console($first_multiple_image);
			debug_to_console($second_multiple_image);
			*/
	
			////////////
			// OUTPUT //
			///////////
			$output = '<div id="hero" class="' . $extra_class . ' view-one">';

				$output .= '<div class="vignetten-wrapper new-style"></div>';

				/* /////////////////// */ 
				/* HERO CONTENT VIEW 1 */
				/* /////////////////// */ 
				$output .= '<div class="hero-content view-one">';
					$output .= '<div class="content-width">';
						// Linke Spalte 
						$output .= '<div class="left66">';
							$output .= '<div class="berding-b deich no-hover">';
								$output .= 	'<div class="person-wrapper">
												<img class="person" src="' . wp_get_attachment_image_url( $testimonial_image_url, $size = 'full' ) . '" alt="">
											</div>';
											
								$output .= '<img class="stone left" src="' . wp_get_attachment_image_url( $first_multiple_image, $size = 'full' ) . '" alt="">';
								$output .= '<img class="stone right" src="' . wp_get_attachment_image_url( $second_multiple_image, $size = 'full' ) . '" alt="">';
								$output .= '<img class="stone back" src="' . wp_get_attachment_image_url( $first_multiple_image, $size = 'full' ) . '" alt="">';

								$output .= '<div class="red-svg-wrapper">
												<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" alt="">
											</div>';
								
								$output .= '<div class="svg-wrapper">
												<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" style="opacity: 0 !important;" alt="">
											</div>';
								// $output .= '<img class="berding-b-for-size z0 absolute image-shadow" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" alt="">';
								
								$output .= 	'</div>';
						$output .= '</div>';
											
						// Rrechte Spalte 
						$output .= 	'<div class="right33">
										<h1>
											' . $heading_one . '
										</h1>
										<p>
											' . $content . ' 
										</p>

										<a href="'. $button_one_url .'" class="btn mehr-erfahren">
											' . $button_one_title . '  
										</a>
									</div>';

					$output .= '</div> <!-- END CONTENT WIDTH -->'; // End Content Width 
					

					// Unteres Hero Menu 
					
					$output .= '<div class="content-width" style="padding-top: 0vh;">
									<div class="left66">
										<div class="arbeitsbereiche">';

					if ($view_name_one) {
						$output .=			'<h3 class="view-name-one">' . $view_name_one . '</h3>';
											// <h3>Verwaltung</h3>
											// <h3>Vertrieb</h3>
					} 
					if ($view_name_two) {
						$output .=			'<h3 class="view-name-two">' . $view_name_two . '</h3>';
					} 
					if ($view_name_three) {
						$output .=			'<h3 class="view-name-three">' . $view_name_three . '</h3>';
					} 
					if ($view_name_four) {
						$output .=			'<h3 class="view-name-four">' . $view_name_four . '</h3>';
					} 

					$output .= 			'</div>
									</div>
								</div>';

						// $output .= '<blockquote cite="' . $blockquote_url . '">';
						// $output .= $content;
						// $output .= '<p>' . $first_multiple_image . ' und ' . $second_multiple_image . '</p>';
						// $output .= '<img src="' . wp_get_attachment_image_url( $image_url, $size = 'full' ) . '" style="width:' . $button_text . ';">';
						// $output .= '<footer>' . $heading_one . ' - <cite><a href="' . $blockquote_url . '">' . $blockquote_title . '</a></cite></footer>';
						// $output .= '</blockquote>';

				$output .= '</div>  <!-- END HERO CONTENT -->'; // End Hero Content 



				/* /////////////////// */ 
				/* HERO CONTENT VIEW 2 */
				/* /////////////////// */ 
				$output .= '<div class="hero-content view-two">';
					$output .= '<div class="content-width">';
						// Linke Spalte 
						$output .= '<div class="left66">';
							$output .= '<div class="berding-b deich no-hover">';
								$output .= 	'<div class="person-wrapper">
												<img class="person" src="' . wp_get_attachment_image_url( $testimonial_image_url_two, $size = 'full' ) . '" alt="">
											</div>';
											
								$output .= '<img class="stone left" src="' . wp_get_attachment_image_url( $first_multiple_image_two, $size = 'full' ) . '" alt="">';
								$output .= '<img class="stone right" src="' . wp_get_attachment_image_url( $second_multiple_image_two, $size = 'full' ) . '" alt="">';
								$output .= '<img class="stone back" src="' . wp_get_attachment_image_url( $myThirdMultipleImageArray[2], $size = 'full' ) . '" alt="">';

								$output .= '<div class="red-svg-wrapper">
												<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" alt="">
											</div>';
								
								$output .= '<div class="svg-wrapper">
												<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" style="opacity: 0 !important;" alt="">
											</div>';
								// $output .= '<img class="berding-b-for-size z0 absolute image-shadow" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" alt="">';
								
								$output .= 	'</div>';
						$output .= '</div>';
											
						// Rrechte Spalte 
						$output .= 	'<div class="right33">
										<h1>
											' . $heading_two . '
										</h1>
										<p>
											' . $content . ' 
										</p>

										<a href="'. $button_two_url .'" class="btn mehr-erfahren">
											' . $button_two_title . '  
										</a>
									</div>';

					$output .= '</div> <!-- END CONTENT WIDTH -->'; // End Content Width 
					

					// Unteres Hero Menu 
					
					$output .= '<div class="content-width" style="padding-top: 0vh;">
									<div class="left66">
										<div class="arbeitsbereiche">';

					if ($view_name_one) {
						$output .=			'<h3 class="view-name-one">' . $view_name_one . '</h3>';
											// <h3>Verwaltung</h3>
											// <h3>Vertrieb</h3>
					} 
					if ($view_name_two) {
						$output .=			'<h3 class="view-name-two">' . $view_name_two . '</h3>';
					} 
					if ($view_name_three) {
						$output .=			'<h3 class="view-name-three">' . $view_name_three . '</h3>';
					} 
					if ($view_name_four) {
						$output .=			'<h3 class="view-name-four">' . $view_name_four . '</h3>';
					} 

					$output .= 			'</div>
									</div>
								</div>';

				$output .= '</div>  <!-- END HERO CONTENT -->'; // End Hero Content 



				/* /////////////////// */ 
				/* HERO CONTENT VIEW 3 */ 
				/* /////////////////// */ 
				$output .= '<div class="hero-content view-three">';
					$output .= '<div class="content-width">';
						// Linke Spalte 
						$output .= '<div class="left66">';
							$output .= '<div class="berding-b deich no-hover">';
								$output .= 	'<div class="person-wrapper">
												<img class="person" src="' . wp_get_attachment_image_url( $testimonial_image_url_three, $size = 'full' ) . '" alt="">
											</div>';
											
								$output .= '<img class="stone left" src="' . wp_get_attachment_image_url( $first_multiple_image_three, $size = 'full' ) . '" alt="">';
								$output .= '<img class="stone right" src="' . wp_get_attachment_image_url( $second_multiple_image_three, $size = 'full' ) . '" alt="">';
								$output .= '<img class="stone back" src="' . wp_get_attachment_image_url( $first_multiple_image_three, $size = 'full' ) . '" alt="">';

								$output .= '<div class="red-svg-wrapper">
												<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" alt="">
											</div>';
								
								$output .= '<div class="svg-wrapper">
												<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" style="opacity: 0 !important;" alt="">
											</div>';
								// $output .= '<img class="berding-b-for-size z0 absolute image-shadow" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" alt="">';
								
								$output .= 	'</div>';
						$output .= '</div>';
											
						// Rrechte Spalte 
						$output .= 	'<div class="right33">
										<h1>
											' . $heading_three . '
										</h1>
										<p>
											' . $content . ' 
										</p>

										<a href="'. $button_three_url .'" class="btn mehr-erfahren">
											' . $button_three_title . '  
										</a>
									</div>';

					$output .= '</div> <!-- END CONTENT WIDTH -->'; // End Content Width 
					

					// Unteres Hero Menu 
					
					$output .= '<div class="content-width" style="padding-top: 0vh;">
									<div class="left66">
										<div class="arbeitsbereiche">';

					if ($view_name_one) {
						$output .=			'<h3 class="view-name-one">' . $view_name_one . '</h3>';
											// <h3>Verwaltung</h3>
											// <h3>Vertrieb</h3>
					} 
					if ($view_name_two) {
						$output .=			'<h3 class="view-name-two">' . $view_name_two . '</h3>';
					} 
					if ($view_name_three) {
						$output .=			'<h3 class="view-name-three">' . $view_name_three . '</h3>';
					} 
					if ($view_name_four) {
						$output .=			'<h3 class="view-name-four">' . $view_name_four . '</h3>';
					} 

					$output .= 			'</div>
									</div>
								</div>';

				$output .= '</div>  <!-- END HERO CONTENT -->'; // End Hero Content 
				



				/* /////////////////// */ 
				/* HERO CONTENT VIEW 4 */ 
				/* /////////////////// */ 
				$output .= '<div class="hero-content view-four">';
					$output .= '<div class="content-width">';
						// Linke Spalte 
						$output .= '<div class="left66">';
							$output .= '<div class="berding-b deich no-hover">';
								$output .= 	'<div class="person-wrapper">
												<img class="person" src="' . wp_get_attachment_image_url( $testimonial_image_url_four, $size = 'full' ) . '" alt="">
											</div>';
											
								$output .= '<img class="stone left" src="' . wp_get_attachment_image_url( $first_multiple_image_four, $size = 'full' ) . '" alt="">';
								$output .= '<img class="stone right" src="' . wp_get_attachment_image_url( $second_multiple_image_four, $size = 'full' ) . '" alt="">';
								$output .= '<img class="stone back" src="' . wp_get_attachment_image_url( $first_multiple_image_four, $size = 'full' ) . '" alt="">';

								$output .= '<div class="red-svg-wrapper">
												<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" alt="">
											</div>';
								
								$output .= '<div class="svg-wrapper">
												<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" style="opacity: 0 !important;" alt="">
											</div>';
								// $output .= '<img class="berding-b-for-size z0 absolute image-shadow" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" alt="">';
								
								$output .= 	'</div>';
						$output .= '</div>';
											
						// Rrechte Spalte 
						$output .= 	'<div class="right33">
										<h1>
											' . $heading_four . '
										</h1>
										<p>
											' . $content . ' 
										</p>

										<a href="'. $button_four_url .'" class="btn mehr-erfahren">
											' . $button_four_title . '  
										</a>
									</div>';

					$output .= '</div> <!-- END CONTENT WIDTH -->'; // End Content Width 
					

					// Unteres Hero Menu 
					
					$output .= '<div class="content-width" style="padding-top: 0vh;">
									<div class="left66">
										<div class="arbeitsbereiche">';

					if ($view_name_one) {
						$output .=			'<h3 class="view-name-one">' . $view_name_one . '</h3>';
											// <h3>Verwaltung</h3>
											// <h3>Vertrieb</h3>
					} 
					if ($view_name_two) {
						$output .=			'<h3 class="view-name-two">' . $view_name_two . '</h3>';
					} 
					if ($view_name_three) {
						$output .=			'<h3 class="view-name-three">' . $view_name_three . '</h3>';
					} 
					if ($view_name_four) {
						$output .=			'<h3 class="view-name-four">' . $view_name_four . '</h3>';
					} 

					$output .= 			'</div>
									</div>
								</div>';

				$output .= '</div>  <!-- END HERO CONTENT -->'; // End Hero Content 


			$output .= '</div>'; // End #hero HTML

			$output .= 	'
							<style>
							/* Position the Persons/Testimonials */ 
							/* !!! HIER UNBEDINGT NOCH WERTE AUS BACKEND MODUL (WP_BACKERY) AUSGEBEN !!! */ 
							
							.hero-content.view-one .berding-b .person-wrapper {
								margin-left: -8%;
							}
							.hero-content.view-two .berding-b .person-wrapper {
								margin-left: -10%;
							}
							.hero-content.view-three .berding-b .person-wrapper {
								margin-left: -8%;
							}
							.hero-content.view-four .berding-b .person-wrapper {
								margin-left: -10%;
							}

							.hero-content.view-one .person-wrapper img.person {
								margin-left: ' . $testimonial_position_one . ';
							}
							.hero-content.view-two .person-wrapper img.person {
								margin-left: ' . $testimonial_position_two . ';
							}
							.hero-content.view-three .person-wrapper img.person {
								margin-left: ' . $testimonial_position_three . ';
							}
							.hero-content.view-four .person-wrapper img.person {
								margin-left: ' . $testimonial_position_four . ';
							}
							</style>
						';

			$output .= 	/*html*/'<style>
							/* ///////////////////////////////////////////////////////// */
							/* show and hide View (e.g. "view-one" or "view-two") Blocks */
							/* ///////////////////////////////////////////////////////// */
							
							/* Whole Content View */ 
							#hero .hero-content {
								opacity: 0;
								z-index: 0;
								transition: all 900ms 0ms, opacity 900ms 900ms, z-index 0ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-one .hero-content.view-one {
								opacity: 1;
								z-index: 9;								
								transition: all 900ms 0ms, opacity 900ms 0ms, z-index 0ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-two .hero-content.view-two {
								opacity: 1;
								z-index: 9;								
								transition: all 900ms 0ms, opacity 900ms 0ms, z-index 0ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-three .hero-content.view-three {
								opacity: 1;
								z-index: 9;								
								transition: all 900ms 0ms, opacity 900ms 0ms, z-index 0ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-four .hero-content.view-four {
								opacity: 1;
								z-index: 9;								
								transition: all 900ms 0ms, opacity 900ms 0ms, z-index 0ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}


							/* Berding B */ 
							/*
							#hero.change-transition .hero-content .berding-b img {
								opacity: 0;
								transition: all 900ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-one.change-transition .hero-content.view-one .berding-b img {
								opacity: 1;
								transition: all 900ms 1800ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-two.change-transition .hero-content.view-two .berding-b img {
								opacity: 1;
								transition: all 900ms 1800ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-three.change-transition .hero-content.view-three .berding-b img {
								opacity: 1;
								transition: all 900ms 1800ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-four.change-transition .hero-content.view-four .berding-b img {
								opacity: 1;
								transition: all 900ms 1800ms, height 0ms 0ms, width 0ms 0ms;
							}
							*/



							/* FLYING IMG WRAPPER ANIMATION */ 
							#hero.change-transition .hero-content .berding-b img.stone {
								opacity: 0;
								transition: all 900ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-one.change-transition .hero-content.view-one .berding-b img.stone {
								opacity: 1;
								transition: all 1800ms 900ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-two.change-transition .hero-content.view-two .berding-b img.stone {
								opacity: 1;
								transition: all 1800ms 900ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-three.change-transition .hero-content.view-three .berding-b img.stone {
								opacity: 1;
								transition: all 1800ms 900ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-four.change-transition .hero-content.view-four .berding-b img.stone {
								opacity: 1;
								transition: all 1800ms 900ms, height 0ms 0ms, width 0ms 0ms;
							}



							/* PERSON WRAPPER ANIMATION */ 
							#hero.change-transition .hero-content .berding-b .person-wrapper {
								opacity: 0;
								transition: all 900ms 0ms, opacity 900ms  height 0ms 0ms, width 0ms 0ms;
								transition: all 0ms 1200ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
								clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0 100%); /* from bottom */ 
							}
							#hero.view-one.change-transition .hero-content.view-one .berding-b .person-wrapper {
								opacity: 1;
								transition: all 1800ms 900ms, height 0ms 0ms, width 0ms 0ms;
								transition: all 1200ms 0ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
								clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
							}
							#hero.view-two.change-transition .hero-content.view-two .berding-b .person-wrapper {
								opacity: 1;
								transition: all 1800ms 900ms, height 0ms 0ms, width 0ms 0ms;
								transition: all 1200ms 0ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
								clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
							}
							#hero.view-three.change-transition .hero-content.view-three .berding-b .person-wrapper {
								opacity: 1;
								transition: all 1800ms 900ms, height 0ms 0ms, width 0ms 0ms;
								transition: all 1200ms 0ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
								clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
							}
							#hero.view-four.change-transition .hero-content.view-four .berding-b .person-wrapper {
								opacity: 1;
								transition: all 1800ms 900ms, height 0ms 0ms, width 0ms 0ms;
								transition: all 1200ms 0ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
								clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
							}


							/* RED SVG WRAPPER ANIMATION */ 
							#hero.change-transition .hero-content .berding-b .red-svg-wrapper {
								opacity: 0;
								clip-path: polygon(-500% 0%, 0% 0, 0% 100%, -500% 100%);
								clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0 100%); /* from bottom */ 
								transition: all 0ms 1200ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
							}
							#hero.view-one.change-transition .hero-content.view-one .berding-b .red-svg-wrapper {
								opacity: 0;
								clip-path: polygon(100% 0, 120% 0%, 120% 100%, 100% 100%);
								clip-path: polygon(0 0, 100% 0, 100% 0%, 0 0%); /* from bottom to top */ 
								clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%); /* from bottom */ 
								transition: all 1200ms 0ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
							}
							#hero.view-two.change-transition .hero-content.view-two .berding-b .red-svg-wrapper {
								opacity: 0;
								clip-path: polygon(100% 0, 120% 0%, 120% 100%, 100% 100%);
								clip-path: polygon(0 0, 100% 0, 100% 0%, 0 0%); /* from bottom to top */ 
								clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%); /* from bottom */ 
								transition: all 1200ms 0ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
							}
							#hero.view-three.change-transition .hero-content.view-three .berding-b .red-svg-wrapper {
								opacity: 0;
								clip-path: polygon(100% 0, 120% 0%, 120% 100%, 100% 100%);
								clip-path: polygon(0 0, 100% 0, 100% 0%, 0 0%); /* from bottom to top */ 
								clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%); /* from bottom */ 
								transition: all 1200ms 0ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
							}
							#hero.view-four.change-transition .hero-content.view-four .berding-b .red-svg-wrapper {
								opacity: 0;
								clip-path: polygon(100% 0, 120% 0%, 120% 100%, 100% 100%);
								clip-path: polygon(0 0, 100% 0, 100% 0%, 0 0%); /* from bottom to top */ 
								clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%); /* from bottom */ 
								transition: all 1200ms 0ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms, display 0ms 0ms;
							}


							/* SVG IMAGE WRAPPER */ 
							#hero.change-transition .hero-content .berding-b .svg-wrapper {
								clip-path: polygon(0 0, 0 0, 0 100%, 0 100%);
								clip-path: polygon(0 100%, 100% 100%, 100% 100%, 0 100%); /* from bottom */ 
								filter: blur(6px);
								filter: blur(12px) brightness(4);
								transition: all 0ms 1200ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-one.change-transition .hero-content.view-one .berding-b .svg-wrapper {
								clip-path: polygon(-15% -15%, 100% -15%, 100% 100%, 0 100%);
								filter: blur(0px);
								transition: all 1200ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-two.change-transition .hero-content.view-two .berding-b .svg-wrapper {
								clip-path: polygon(-15% -15%, 100% -15%, 100% 100%, 0 100%);
								filter: blur(0px);
								transition: all 1200ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-three.change-transition .hero-content.view-three .berding-b .svg-wrapper {
								clip-path: polygon(-15% -15%, 100% -15%, 100% 100%, 0 100%);
								filter: blur(0px);
								transition: all 1200ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-four.change-transition .hero-content.view-four .berding-b .svg-wrapper {
								clip-path: polygon(-15% -15%, 100% -15%, 100% 100%, 0 100%);
								filter: blur(0px);
								transition: all 1200ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}

							/* Animation H1 */ 
							#hero.change-transition h1 {
								opacity: 0;
								letter-spacing: 0.2vw;
								transform: translate(-1vw, 0px);
								transition: all 0ms 1800ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-one.change-transition .hero-content.view-one h1 {	
								opacity: 1;															
								letter-spacing: 0vw;
    							transform: translate(0vw, 0px);
								transition: all 1800ms 0ms, opacity 900ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-two.change-transition .hero-content.view-two h1 {	
								opacity: 1;															
								letter-spacing: 0vw;
    							transform: translate(0vw, 0px);
								transition: all 1800ms 0ms, opacity 900ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-three.change-transition .hero-content.view-three h1 {	
								opacity: 1;															
								letter-spacing: 0vw;
    							transform: translate(0vw, 0px);
								transition: all 1800ms 0ms, opacity 900ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-four.change-transition .hero-content.view-four h1 {
								opacity: 1;															
								letter-spacing: 0vw;
    							transform: translate(0vw, 0px);
								transition: all 1800ms 0ms, opacity 900ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}

							/* Animation right33 p */ 
							#hero.change-transition .right33 p {
								opacity: 0;
								transition: all 0ms 1800ms, opacity 0ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-one.change-transition .hero-content.view-one .right33 p {	
								opacity: 1;															
								transition: all 1800ms 900ms, opacity 900ms 900ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-two.change-transition .hero-content.view-two .right33 p {	
								opacity: 1;															
								transition: all 1800ms 900ms, opacity 900ms 900ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-three.change-transition .hero-content.view-three .right33 p {	
								opacity: 1;															
								transition: all 1800ms 900ms, opacity 900ms 900ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-four.change-transition .hero-content.view-four .right33 p {
								opacity: 1;															
								transition: all 1800ms 900ms, opacity 900ms 900ms, height 0ms 0ms, width 0ms 0ms;
							}


							/* Animation Hero Button  */ 
							#hero.change-transition .right33 a {
								opacity: 0;
								transform: translate(0vw, 1vh);
								transition: all 0ms 0ms, background 240ms 0ms, color 240ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-one.change-transition .hero-content.view-one .right33 a {	
								opacity: 1;									
								transform: translate(0vw, 0vh);						
								transition: all 1800ms 1800ms, background 240ms 0ms, color 240ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-two.change-transition .hero-content.view-two .right33 a {	
								opacity: 1;									
								transform: translate(0vw, 0vh);						
								transition: all 1800ms 1800ms, background 240ms 0ms, color 240ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-three.change-transition .hero-content.view-three .right33 a {	
								opacity: 1;								
								transform: translate(0vw, 0vh);							
								transition: all 1800ms 1800ms, background 240ms 0ms, color 240ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}
							#hero.view-four.change-transition .hero-content.view-four .right33 a {
								opacity: 1;
								transform: translate(0vw, 0vh);					
								transition: all 1800ms 1800ms, background 240ms 0ms, color 240ms 0ms, height 0ms 0ms, width 0ms 0ms;
							}

							/* ///////////////// */ 
							/* End Animation CSS */ 
							/* ///////////////// */ 


							.hero-content {
								position: absolute;
								width: 100%;
								z-index: 9;
							}
							
							.berding-b .svg-wrapper { 
								-webkit-mask-image: url(' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . ');
								mask-image: url(' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . ');
							}

							#hero .red-svg-wrapper {
								-webkit-mask-image: url(' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . ');
								mask-image: url(' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . ');
							}

							#hero .background-wrapper .color-layer, #hero .background-wrapper video, #hero .background-wrapper img { 
								-webkit-mask-image: url(' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . ');
								mask-image: url(' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . ');
							}

							#hero .hero-content.view-one .berding-b.deich .svg-wrapper { 
								background-image: url(' . wp_get_attachment_image_url( $image_url, $size = 'full' ) . '); 
							}
							#hero .hero-content.view-two .berding-b.deich .svg-wrapper { 
								background-image: url(' . wp_get_attachment_image_url( $image_url_two, $size = 'full' ) . '); 
							}
							#hero .hero-content.view-three .berding-b.deich .svg-wrapper { 
								background-image: url(' . wp_get_attachment_image_url( $image_url_three, $size = 'full' ) . '); 
								background-position: top left;
							}
							#hero .hero-content.view-four .berding-b.deich .svg-wrapper { 
								background-image: url(' . wp_get_attachment_image_url( $image_url_four, $size = 'full' ) . '); 
								background-position: top left;
							}
						</style>';

			$output .= 	/*html*/'<script>
							onload = (event) => { 
								console.log("Window loaded")
					
								// const header = document.querySelector("#header")
								// console.log("header: ", header)
					
								const hero = document.querySelector("#hero")
					
								setTimeout(() => {
									// header.classList.add("show");
									hero.classList.add("show");
								}, "100")

								setTimeout(() => {
									// header.classList.add("show");
									hero.classList.add("change-transition");
								}, "3200")
							}

							/* ////////////////////////// */ 
							/* Functions to show and hide */ 
							/* ////////////////////////// */ 
							
							const showViewOne = () => {
								hero.classList.add("view-one")
								hero.classList.remove("view-two")
								hero.classList.remove("view-three")
								hero.classList.remove("view-four")
							}
							const showViewTwo = () => {
								hero.classList.remove("view-one")
								hero.classList.add("view-two")
								hero.classList.remove("view-three")
								hero.classList.remove("view-four")
							}
							const showViewThree = () => {
								hero.classList.remove("view-one")
								hero.classList.remove("view-two")
								hero.classList.add("view-three")
								hero.classList.remove("view-four")
							}
							const showViewFour = () => {
								hero.classList.remove("view-one")
								hero.classList.remove("view-two")
								hero.classList.remove("view-three")
								hero.classList.add("view-four")
							}

							/* ///////////////////// */ 
							/* Get Buttons to change */
							/* ///////////////////// */ 

							// EventListener ViewOne 
							let allViewOneButtons = document.querySelectorAll(".view-name-one")
							if(allViewOneButtons.length) {
								for (let i = 0; i < allViewOneButtons.length; i++) {
									allViewOneButtons[i].addEventListener("click", showViewOne);
								}
							}

							// EventListener ViewTwo 
							let allViewTwoButtons = document.querySelectorAll(".view-name-two")
							if(allViewTwoButtons.length) {
								for (let i = 0; i < allViewOneButtons.length; i++) {
									allViewTwoButtons[i].addEventListener("click", showViewTwo);
								}
							}

							// EventListener ViewThree 
							let allViewThreeButtons = document.querySelectorAll(".view-name-three")
							if(allViewThreeButtons.length) {
								for (let i = 0; i < allViewOneButtons.length; i++) {
									allViewThreeButtons[i].addEventListener("click", showViewThree);
								}
							}	

							// EventListener ViewFour 
							let allViewFourButtons = document.querySelectorAll(".view-name-four")
							if(allViewFourButtons.length) {
								for (let i = 0; i < allViewOneButtons.length; i++) {
									allViewFourButtons[i].addEventListener("click", showViewFour);
								}
							}


							
						</script>';
	
			return $output;
	
		}
	
	}
	
	new VcSodaBlockquote();

}






/********************* */
//* BERDING BLOCKQUOTE */
/********************* */
if ( ! class_exists( 'BerdingBlockquoteTestimonial' ) ) {

	class BerdingBlockquoteTestimonial extends WPBakeryShortCode {

		function __construct() {
			add_action( 'init', array( $this, 'create_shortcode' ), 999 );
			add_shortcode( 'berding_blockquote_testimonial', array( $this, 'render_shortcode' ) );
		}

		public function create_shortcode() {
			// Stop all if VC is not enabled
			if ( !defined( 'WPB_VC_VERSION' ) ) {
				return;
			}

			// Map blockquote with vc_map()
			vc_map( array(
				'name'          => __('Berding Blockquote Testimonial', 'rr'),
				'base'          => 'berding_blockquote_testimonial',
				'description'  	=> __( '', 'rr' ),
				'category'      => __( 'rr Modules', 'rr'),
				'params' => array(

					// array(
					// 	"type" => "textarea_html",
					// 	"holder" => "div",
					// 	"class" => "",
					// 	"heading" => __( "Blockquote Content", 'rr' ),
					// 	"param_name" => "content", // Important: Only one textarea_html param per content element allowed and it should have "content" as a "param_name"
					// 	"value" => __( "I am test text block. Click edit button to change this text.", 'rr' ),
					// 	"description" => __( "Enter content.", 'rr' )
					// ),

					array(
						"type" => "textarea",
						"holder" => "div",
						"class" => "",
						"heading" => __( "Blockquote Content", 'rr' ),
						"param_name" => "main_textarea", // Important: Only one textarea_html param per content element allowed and it should have "content" as a "param_name"
						"value" => __( "<p>I am test text block. Click edit button to change this text.</p>", 'rr' ),
						"description" => __( "Enter content.", 'rr' )
					),

					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Author Name', 'rr' ),
						'param_name'    => 'quote_author',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Add Author Quote.', 'rr' ),
					),

					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Author Jobs', 'rr' ),
						'param_name'    => 'quote_author_job',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Add Author Quote.', 'rr' ),
					),


					array(
						"type" => "vc_link",
						"class" => "",
						"heading" => __( "Blockquote Link/Button", 'rr' ),
						"param_name" => "blockquote_cite",
						"description" => __( "Add Citiation Link and Source Name", 'rr' ),
					),

					/* Testimonial Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Testimonial Image URL", "rr" ),
						"param_name" => "testimonial_image_url",
						"value" => '',
						"description" => __( "Testimonial Image", "rr" ),
					),

					/*********************** */
					/* EXTRAS | Zusatz Stuff */
					/*********************** */ 
					array(
						"type" => "checkbox",
						"class" => "",
						"heading" => __( "Switch left & right col", "my-text-domain" ),
						"param_name" => "switch_left_right",
						"value" => __( "", "my-text-domain" ),
						"description" => __( "Enter description.", "my-text-domain" ),
						'group'         => __( 'Extra', 'rr'),
					),

					/* extra Class */ 
					array(
						'type'          => 'textfield',
						'heading'       => __( 'Element ID', 'rr' ),
						'param_name'    => 'element_id',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Enter element ID (Note: make sure it is unique and valid).', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),

					array(
						'type'          => 'textfield',
						'heading'       => __( 'Extra class name', 'rr' ),
						'param_name'    => 'extra_class',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Style particular content element differently - add a class name and refer to it in custom CSS.', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),
				),
			));

		}

		public function render_shortcode( $atts ) {
			$atts = (shortcode_atts(array(
				'main_textarea'		=> '',
				'blockquote_cite'   => '',
				'quote_author'      => '',
				'quote_author_job'  => '',
				'testimonial_image_url' => '',

				'switch_left_right' => '',
				'extra_class'       => '',
				'element_id'        => ''
			), $atts));


			//Content
			// $content       		= wpb_js_remove_wpautop($content, true);
			$quote_author       	= esc_html($atts['quote_author']);
			$quote_author_job       = esc_html($atts['quote_author_job']);

			// main_textarea
			$main_textarea 		= esc_html($atts['main_textarea']);

			//Cite Link
			$blockquote_source  = vc_build_link( $atts['blockquote_cite'] );
			$blockquote_title   = esc_html($blockquote_source["title"]);
			$blockquote_url     = esc_url( $blockquote_source['url'] );
			$blockquote_target     = esc_html( $blockquote_source['target'] );

			// Author/Testimonial Image 
			$testimonial_image_url = esc_html($atts['testimonial_image_url']);

			// Check if left and right is switched 
			$switch_left_right 	= esc_attr($atts['switch_left_right']);

			//Class and Id
			$extra_class        = esc_attr($atts['extra_class']);
			$element_id         = esc_attr($atts['element_id']);

			////////////
			// OUTPUT //
			///////////
			$output = '';
			
			$output = '<div class="' . $extra_class . ' ' . $switch_left_right . ' blockquote-testimonial">';

				$output .= '<div class="vignetten-wrapper new-style"></div>';

				$output .= '<div class="content-width">';

					$output .= 	'<div class="left33">
									<div class="person-wrapper">
										<img class="person" src="' . wp_get_attachment_image_url( $testimonial_image_url, $size = 'full' ) . '" alt="">
									</div>
								</div>';

					$output .= '<div class="right66">';
						$output .= '<div class="blockquote ' . $extra_class . '" id="' . $element_id . '" >';
							$output .= '<blockquote cite="' . $blockquote_url . '">';

								// Display content.
								$output .= '<div class="advanced-hero-element__content">';
								// if ( $content ) {
								// 	$output .= wp_kses_post( $content );
								// } else {
								// 	$output .= 'Hello';
								// }
								$output .= '</div>';

								$output .= '<span class="open-quote quote-mark">”</span>';

								$output .= '<p>' . $main_textarea . '</p>';

								$output .= 	'<div class="author">
												<p class="author">' . $quote_author . '</p>
												<p class="author">' . $quote_author_job . '</p>
											</div>';

								if (empty($blockquote_url)) {
									$output .= '<span></span>';
								} else {
									$output .= '<a href="' . $blockquote_url . '" target="'. $blockquote_target .'" class="btn">' . $blockquote_title . '</a>';
									/* $output .= '<p>' .  var_dump($blockquote_source) . '</p>'; */ 
									/* $output .= '<p>' .  $blockquote_target . '</p>'; */ 
								}

							$output .= '</blockquote>';
						$output .= '</div>'; // End Blockquote 
					$output .= '</div>'; // End right66

				$output .= '</div>';
			$output .= '</div>';


			$output .= /*html*/'<style>
							.blockquote-testimonial {
								background: transparent radial-gradient(closest-side at 50% 50%, #162751AF 0%, #162751 150%) 0% 0% no-repeat padding-box;
								background: transparent radial-gradient(closest-side at 50% 50%, #535c74 0%, #162751 150%) 0% 0% no-repeat padding-box;
								/* padding: 4%; */ 
								margin: auto;
							}

							/* IF true -> for row reverse | left-right */ 
							.true.blockquote-testimonial .content-width {
								display: flex;
								flex-direction: row-reverse;
							}

							.blockquote-testimonial .vignetten-wrapper {
								height: 100%;
								position: absolute;
								width: 100%;
								z-index: 0;
								box-shadow: 0 0 83vw rgb(0 0 0 / 50%) inset;
							}

							.blockquote-testimonial .right66 {
								align-items: center;
								display: flex;
							}

							.blockquote-testimonial blockquote {
								text-align: center;
   								margin: auto;
							}
							.blockquote-testimonial blockquote::before {
								border: none;
							}

							.blockquote-testimonial blockquote span.open-quote {
								font-size: 110px;
								line-height: 110px;
								margin-bottom: -30px;
								font-weight: 900;
								font-family: "Montserat", sans-serif;
								color: #fff;
							}
							.blockquote-testimonial blockquote p {
								color: #fff;
								text-align: center;
								font-weight: 500;
							}
							.blockquote-testimonial blockquote p.author {
								color: #fff;
								text-align: center;
								font-weight: 700;
							}

							.blockquote-testimonial .person-wrapper {
								position: relative;
								height: 100% !important;
								position: flex;
								display: flex;
								vertical-align: baseline;
								align-items: baseline;
								// margin-top: calc( -11% - 1px);
							}

							.blockquote-testimonial .person-wrapper img.person {

							}

							.blockquote-testimonial .author {
								margin-bottom: 6%;
							}
							.blockquote-testimonial p.author {
								margin-bottom: 0px;
								padding-bottom: 0px;
								line-height: 27px;
							}

							@media screen and (max-width: 999px) {
								.blockquote-testimonial .left33 {
									display: none;
								}
								.blockquote-testimonial .right66 blockquote {
									text-align: center;
									margin: auto;
									margin-top: 6%;
									padding: 0px 30px 50px 30px;
								}
							}

							
			
						</style>
						';

			return $output;

		}

	}

	new BerdingBlockquoteTestimonial();

}



/********************** */
/* BERDING B JOB TEASER */ 
/********************** */
if ( ! class_exists( 'BerdingBereichTeaser' ) ) {

	class BerdingBereichTeaser extends WPBakeryShortCode {

		function __construct() {
			add_action( 'init', array( $this, 'create_shortcode' ), 999 );
			add_shortcode( 'berding_b_teaser_module', array( $this, 'render_shortcode' ) );
		}

		public function create_shortcode() {
			// Stop all if VC is not enabled
			if ( !defined( 'WPB_VC_VERSION' ) ) {
				return;
			}

			// Map blockquote with vc_map()
			vc_map( array(
				'name'          => __('BERDING B Teaser (z.B. für Bereiche)', 'rr'),
				'base'          => 'berding_b_teaser_module',
				'description'  	=> __( '', 'rr' ),
				'category'      => __( 'rr Modules', 'rr'),
				'params' => array(

					// array(
					// 	"type" => "textarea_html",
					// 	"holder" => "div",
					// 	"class" => "",
					// 	"heading" => __( "Blockquote Content", 'rr' ),
					// 	"param_name" => "content", // Important: Only one textarea_html param per content element allowed and it should have "content" as a "param_name"
					// 	"value" => __( "I am test text block. Click edit button to change this text.", 'rr' ),
					// 	"description" => __( "Enter content.", 'rr' )
					// ),

					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Heading (Pflichtfeld und es muss einzigartig auf dieser Seite in diesem Modul verwendet werden)', 'rr' ),
						'param_name'    => 'text_one',
						'value'         => __( '', 'rr' ),
						'description'   => __( '', 'rr' ),
					),

					array(
						"type" => "textarea",
						"holder" => "div",
						"class" => "",
						"heading" => __( "Content", 'rr' ),
						"param_name" => "main_textarea", // Important: Only one textarea_html param per content element allowed and it should have "content" as a "param_name"
						"value" => __( "I am test text block. Click edit button to change this text.", 'rr' ),
						"description" => __( "Enter content.", 'rr' )
					),

					array(
						"type" => "vc_link",
						"class" => "",
						"heading" => __( "Button", 'rr' ),
						"param_name" => "btn_link",
						"description" => __( "Add Citiation Link and Source Name", 'rr' ),
					),

					/* ////////// */ 
					/* Mask Image */ 
					/* ////////// */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Mask Image URL", "rr" ),
						"param_name" => "mask_image_url",
						"value" => '',
						"description" => __( "Mask Image (wird benutzt, um das Hintergrundbild zu maskieren) – Wichtig: Benutze ein SVG!", "rr" )
					),

					/* Background Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Image URL", "rr" ),
						"param_name" => "image_url",
						"value" => '',
						"description" => __( "Enter description.", "my-text-domain" )
					),
					/* Background Image Position */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Background Position (top, bottom etc.)', 'rr' ),
						'param_name'    => 'background_position',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Background Position', 'rr' ),
					),

					/* Testimonial Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Testimonial Image URL", "rr" ),
						"param_name" => "testimonial_image_url",
						"value" => '',
						"description" => __( "Testimonial Image", "rr" )
					),
					/* Horizontale Positionierung Testimonial */ 
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Horizontale Positionierung des Testimonials (include unit e.g. px, vw etc.)', 'rr' ),
						'param_name'    => 'margin_left',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Horizontale Positionierung der Person.', 'rr' ),
					),
					/* Horizontale Positionierung Testimonial */ 
					array(
						'type'          => 'textfield',
						'heading'       => __( 'Vertikale Positionierung des Testimonials (include unit e.g. px, vw etc.)', 'rr' ),
						'param_name'    => 'margin_top',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Vertikale Positionierung der Person.', 'rr' ),
					),

					/* Multiple Images for flying stuff */ 
					array(
						"type" => "attach_images",
						"class" => "",
						"heading" => __( "Multiple Image URLs", "rr" ),
						"param_name" => "multiple_images",
						"value" => '',
						"description" => __( "Multiple Images für fligende Effekte (maximal 3 Bilder – alle weiteren werden nicht berücksichtigt).", "rr" )
					),


					/* Zusatz Stuff */ 
					array(
						"type" => "checkbox",
						"class" => "",
						"heading" => __( "Switch left & right col", "my-text-domain" ),
						"param_name" => "switch_left_right",
						"value" => __( "", "my-text-domain" ),
						"description" => __( "Enter description.", "my-text-domain" ),
						'group'         => __( 'Extra', 'rr'),
					),

					array(
						'type'          => 'textfield',
						'heading'       => __( 'Element ID', 'rr' ),
						'param_name'    => 'element_id',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Enter element ID (Note: make sure it is unique and valid).', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),

					array(
						'type'          => 'textfield',
						'heading'       => __( 'Extra class name', 'rr' ),
						'param_name'    => 'extra_class',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Style particular content element differently - add a class name and refer to it in custom CSS.', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),
				),
			));

		}

		public function render_shortcode( $atts ) {
			$atts = (shortcode_atts(array(
				'main_textarea'		=> '',
				'btn_link'   => '',
				'text_one'      => '',
				'margin_left'  => '',
				'margin_top'  => '',
				'mask_image_url' => '',
				'image_url' => '',
				'background_position' => '',
				'testimonial_image_url' => '',
				'multiple_images' => '',

				'switch_left_right' => '',
				'extra_class'       => '',
				'element_id'        => ''
			), $atts));


			//Content

			// Mask Image 
			$mask_image_url = esc_html($atts['mask_image_url']);

			// Background Image  
			$image_url = esc_html($atts['image_url']);
			// Background Image Position 
			$background_position = esc_html($atts['background_position']);
			
			// Testimonial Image 
			$testimonial_image_url = esc_html($atts['testimonial_image_url']);
			
			// Multiple Images 
			$multiple_images 	= esc_html($atts['multiple_images']);

			$first_multiple_image = substr($multiple_images, 0, strpos($multiple_images, ','));
			$second_multiple_image = substr($multiple_images, strpos($multiple_images, ',') + 1, 99);


			// $content     = wpb_js_remove_wpautop($content, true);
			$text_one       = esc_html($atts['text_one']);
			$margin_left    = esc_html($atts['margin_left']);
			$margin_top		= esc_html($atts['margin_top']);

			// main_textarea
			// $main_textarea 		= esc_html($atts['main_textarea']);
			$main_textarea		= wpb_js_remove_wpautop($atts['main_textarea'], true);

			//SOFTGARDEN API Link (weiß noch gar nicht ob ich sie brauche)
			$btn_link  = vc_build_link( $atts['btn_link'] );
			$btn_title   = esc_html($btn_link["title"]);
			$btn_url     = esc_url( $btn_link['url'] );



			/***************/
			/* Extra Stuff */ 
			/***************/

			// Check if left and right is switched 
			$switch_left_right 	= esc_attr($atts['switch_left_right']);

			//Class and Id
			$extra_class        = esc_attr($atts['extra_class']);
			$element_id         = esc_attr($atts['element_id']);

			////////////
			// OUTPUT //
			///////////
			$output = '<div class="berding-teaser">';
			
			$output .= 		'<div class="berufsfelder-b ' . $switch_left_right . '">';

			$output .=			'
								<!-- <div class="fullwidth"> --> 

										<div class="content-width  jobs-im-fokus ' . $text_one . '">

											<div class="left50">
												<a href="' . $btn_url . '">
													<div class="berding-b production">';
			$output .= 									'<div class="person-wrapper">
															<img class="person" src="' . wp_get_attachment_image_url( $testimonial_image_url, $size = 'full' ) . '" alt="">
														</div>';
												
			$output .= 									'<img class="stone left" src="' . wp_get_attachment_image_url( $first_multiple_image, $size = 'full' ) . '" alt="">';
			$output .= 									'<img class="stone right" src="' . wp_get_attachment_image_url( $second_multiple_image, $size = 'full' ) . '" alt="">';
			$output .= 									'<img class="stone back" src="' . wp_get_attachment_image_url( $first_multiple_image, $size = 'full' ) . '" alt="">';
	
			$output .= 									'<div class="red-svg-wrapper">
															<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" alt="">
														</div>';
									
			$output .= 									'<div class="svg-wrapper">
															<img class="berding-b-for-size" src="' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . '" style="opacity: 0 !important;" alt="">
														</div>';
			$output .=								'</div>
												</a>
											</div> <!-- End left -->

											<div class="right50">
												<h3>' . $text_one . '</h3>
												' . $main_textarea . '
												<a href="' . $btn_url . '" class="btn">
													<button>
														<img class="stone left" src="' . wp_get_attachment_image_url( $first_multiple_image, $size = 'full' ) . '" alt="">
														<img class="stone right" src="' . wp_get_attachment_image_url( $second_multiple_image, $size = 'full' ) . '" alt="">
														' . $btn_title . '
													</button>
												</a>
											</div>

										</div> <!-- End content-width -->

									<!-- </div> End fullwidth -->

								</div> <!-- End berufsfelder-b -->
								';


			$output .= 			'<style>
									.berufsfelder-b .berding-b .person-wrapper {
										position: absolute;
										z-index: 9;
										height: 115%;
										margin-top: calc(-10% + 1px);
										margin-top: calc(-11% + 0px);
										margin-left: -5vw;
										align-items: baseline;
										overflow: hidden;
									}

									.berufsfelder-b .content-width.' . $text_one . ' .person-wrapper img.person {
										margin-left: ' . $margin_left . ';
									}

									.berufsfelder-b .content-width.' . $text_one . ' .person-wrapper {
										margin-top: ' . $margin_top . ' !important;
									}

									.berufsfelder-b .content-width.' . $text_one . ' .berding-b.production .svg-wrapper {
										background-image: url(' . wp_get_attachment_image_url( $image_url, $size = 'full' ) . ');
										background-position: ' . $background_position . ';
									}

									.berufsfelder-b .berding-b .svg-wrapper {
										filter: drop-shadow(6px 4px 3px rgba(0,0,0,.33));
										/* background-size: 110%; */
										background-size: 115%;
										background-position: -50px -50px;
										background-repeat: no-repeat;
										width: 100%;
										height: 100%;
										z-index: 1;
										-webkit-mask-repeat: no-repeat;
										mask-repeat: no-repeat;
										filter: contrast(100%) grayscale(0%) saturate(100%);
										transition: all 240ms ease-in-out;
									}

									.berding-b .svg-wrapper { 
										-webkit-mask-image: url(' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . ');
										mask-image: url(' . wp_get_attachment_image_url( $mask_image_url, $size = 'full' ) . ');
									}

									.berufsfelder-b .berding-b img {
										pointer-events: none;
									}

									.berufsfelder-b a.btn button, .berufsfelder-b a.btn:hover button {
										background: rgba(255,255,255,0);
										margin-top: 0px;
									}

									/* Changed Direction / Switch left & right */ 
									.berufsfelder-b.true .content-width {
										display: flex;
										flex-direction: row-reverse;
									}
									.berufsfelder-b.true .right50 {
										margin-left: 0% !important;
										width: 50%;
										padding-right: 4.5%;
									}

									/*******************/ 
									/* MOBILE B TEASER */
									/*******************/
									@media screen and (max-width: 999px) {
										/* GROBES LAYOUT */ 
										.berufsfelder-b .content-width {
											width: 100%;
											display: block;
										}

										.berufsfelder-b .content-width.jobs-im-fokus .left50, .berufsfelder-b .content-width.jobs-im-fokus .right50 {
											width: 100%;
										}

										.berufsfelder-b .content-width.jobs-im-fokus .left50 {
											margin-bottom: 30px;
										}

										/* Row Reversed */ 
										.berufsfelder-b.true .content-width {
											display: block !important;
											flex-direction: column; 
										}

										/* EINZELNE ELEMENTE */ 
										/* Person */ 
										.berufsfelder-b .berding-b .person-wrapper {
											position: absolute;
											z-index: 9;
											height: 115%;
											/* margin-top: calc(-10% + 1px); */
											margin-top: calc(-12% + -2px);
											margin-left: -10vw;
										}
										.berding-b .person-wrapper img.person {
											height: 105%;
											margin-top: calc(5% - 10px);
										}

										/* Background Image Size */ 
										.berufsfelder-b .content-width .berding-b .svg-wrapper {
											background-size: 150% !important;
										}
									}

								</style>


						';

			$output .= '</div>'; // End .berding-teaser HTML


			/* 
			$output .= /*html---/"<script type='module' src='/wp-content/themes/salient-child/softgarden-app.js'> </script>

								<link rel='stylesheet' href='/wp-content/themes/salient-child/stellenanzeigen.css'>

								";
			*/

			return $output;

		}

	}

	new BerdingBereichTeaser();

}





/************************ */
/* BERDING Video Lightbox */ 
/************************ */
if ( ! class_exists( 'BerdingVideoLightbox' ) ) {

	class BerdingVideoLightbox extends WPBakeryShortCode {

		function __construct() {
			add_action( 'init', array( $this, 'create_shortcode' ), 999 );
			add_shortcode( 'berding_video_lightbox_modul', array( $this, 'render_shortcode' ) );
		}

		public function create_shortcode() {
			// Stop all if VC is not enabled
			if ( !defined( 'WPB_VC_VERSION' ) ) {
				return;
			}

			// Map blockquote with vc_map()
			vc_map( array(
				'name'          => __('BERDING Video Lightbox (vor allem für Jobvideos gedacht)', 'rr'),
				'base'          => 'berding_video_lightbox_modul',
				'description'  	=> __( '', 'rr' ),
				'category'      => __( 'rr Modules', 'rr'),
				'params' => array(

					// array(
					// 	"type" => "textarea_html",
					// 	"holder" => "div",
					// 	"class" => "",
					// 	"heading" => __( "Blockquote Content", 'rr' ),
					// 	"param_name" => "content", // Important: Only one textarea_html param per content element allowed and it should have "content" as a "param_name"
					// 	"value" => __( "I am test text block. Click edit button to change this text.", 'rr' ),
					// 	"description" => __( "Enter content.", 'rr' )
					// ),

					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'ID* (Pflichtfeld!)', 'rr' ),
						'param_name'    => 'signature_id',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Es muss einzigartig auf dieser Seite in diesem Modul verwendet werden und darf keine Leer- oder Sonderzeichen enthalten', 'rr' ),
					),

					/* Thumbnail Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Thumbnail Image URL", "rr" ),
						"param_name" => "thumbnail_image_url",
						"value" => '',
						"description" => __( "The Image you see before video start", "my-text-domain" )
					),

					/* MP4 Video URL */ 
					array(
						'type'          => 'textfield',
						'heading'       => __( 'MP4 Video URL (Pflichtfeld!)', 'rr' ),
						'param_name'    => 'video_url',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'MP4 Datei aus Mediathek. Bitte relative Links benutzen (erst ab dem Slash nach der Domain)!', 'rr' ),
					),
					/* WebM Video URL */ 
					array(
						'type'          => 'textfield',
						'heading'       => __( 'WebM Video URL (Pflichtfeld)', 'rr' ),
						'param_name'    => 'webm_video_url',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'MP4 Datei aus Mediathek. Bitte relative Links benutzen (erst ab dem Slash nach der Domain)!', 'rr' ),
					),


					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Keyword für Stellenanzeigenlink (Dieses Feld wird als Parameter für den Link der Endcard genutzt. Es sollte somit mit dem Namen oder Keywords einer oder mehrerer Stellenanzeigen matchen)', 'rr' ),
						'param_name'    => 'stellenanzeigen_parameter',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Add Author Quote.', 'rr' ),
					),

					/* Ankerlink statt link zur Stellenbörsen */ 
					array(
						"type" => "checkbox",
						"class" => "",
						"heading" => __( "Benutze Ankerlink unter dem Video und in der Endcard statt dem default Stellenbörsenlink", "my-text-domain" ),
						"param_name" => "ankerlink_checkbox",
						"value" => __( "", "my-text-domain" ),
						"description" => __( "Hiermit wird der Link zur Stellenbörse durch einen Ankerlink ersätzt.", "my-text-domain" ),
					),
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Ankerlink (z.B. -> #stellenbörse', 'rr' ),
						'param_name'    => 'ankerlink_parameter',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Damit der Ankerlink, statt dem Link zur Stellenbörse im Frontend ausgegeben wird, muss die Checkbox aktiviert werden', 'rr' ),
					),

					array(
						"type" => "textarea",
						"class" => "",
						"heading" => __( "Endcard Content", 'rr' ),
						"param_name" => "main_textarea", // Important: Only one textarea_html param per content element allowed and it should have "content" as a "param_name"
						"value" => __( "I am test text block. Click edit button to change this text.", 'rr' ),
						"description" => __( "Enter content.", 'rr' )
					),

					

					array(
						"type" => "vc_link",
						"class" => "",
						"heading" => __( "Button", 'rr' ),
						"param_name" => "stellenanzeigen_btn_link",
						"description" => __( "Add Citiation Link and Source Name", 'rr' ),
					),


					/* Zusatz Stuff */ 
					array(
						'type'          => 'textfield',
						'heading'       => __( 'Element ID', 'rr' ),
						'param_name'    => 'element_id',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Enter element ID (Note: make sure it is unique and valid).', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),

					array(
						'type'          => 'textfield',
						'heading'       => __( 'Extra class name', 'rr' ),
						'param_name'    => 'extra_class',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Style particular content element differently - add a class name and refer to it in custom CSS.', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),
				),
			));

		}

		public function render_shortcode( $atts ) {
			$atts = (shortcode_atts(array(
				'signature_id' 			=> '',
				'stellenanzeigen_parameter' => '', 
				'video_url' 			=> '',
				'webm_video_url' 			=> '',

				'main_textarea'			=> '',

				'stellenanzeigen_btn_link'   => '',

				'ankerlink_checkbox' 	=> '',
				'ankerlink_parameter' 	=> '',

				'thumbnail_image_url' => '',
				'background_position' => '',

				'extra_class'       => '',
				'element_id'        => ''
			), $atts));


			//Content

			// Signature URL 
			$signature_id       = esc_html($atts['signature_id']);
			
			// Stellenanzeigen Parameter  
			$stellenanzeigen_parameter       = esc_html($atts['stellenanzeigen_parameter']);

			// Video URLs     = wpb_js_remove_wpautop($content, true);
			$video_url       = esc_html($atts['video_url']);
			$webm_video_url       = esc_html($atts['webm_video_url']);
			
			// Thumbnail Image 
			$thumbnail_image_url = esc_html($atts['thumbnail_image_url']);
			
			

			// main_textarea
			// $main_textarea 		= esc_html($atts['main_textarea']);
			$main_textarea		= esc_html($atts['main_textarea']);

			//SOFTGARDEN API Link (weiß noch gar nicht ob ich sie brauche)
			$stellenanzeigen_btn_link  = vc_build_link( $atts['stellenanzeigen_btn_link'] );
			$stellenanzeigen_btn_title   = esc_html($stellenanzeigen_btn_link["title"]);
			$stellenanzeigen_btn_url     = esc_url( $stellenanzeigen_btn_link['url'] );


			// ANkerlink 
			$ankerlink_checkbox 	= esc_attr($atts['ankerlink_checkbox']);
			$ankerlink_parameter	= esc_html($atts['ankerlink_parameter']);


			/***************/
			/* Extra Stuff */ 
			/***************/

			//Class and Id
			$extra_class        = esc_attr($atts['extra_class']);
			$element_id         = esc_attr($atts['element_id']);

			////////////
			// OUTPUT //
			///////////
			$output = '<div class="berding-video-lightbox-modul">';
			
			$output .= 		'<div class="berding-video-lightbox">';

			
			$output .=	'
								<!-- <div class="fullwidth">

									<div class="content-width"> -->

										<!-- Old Image with old function --> 
										<div class="play-btn" onclick="' . $signature_id .'fireGSAPVideoBox()"></div>
										<img class="berding-video-thumb" 
											onclick="' . $signature_id .'fireGSAPVideoBox()"
											src="' . wp_get_attachment_image_url( $thumbnail_image_url, $size = 'full' )  . '"
										>

										<!-- Old Image with old function  
										<img class="berding-video-thumb" 
											onclick="' . $signature_id .'fireGSAPBox()"
											src="' . wp_get_attachment_image_url( $thumbnail_image_url, $size = 'full' )  . '"
										>
										-->
									
										<!-- <h1>Test Video Lightbox</h1> --> 

										<!-- <a onclick="fireGSAPBox()">fire Lightbox</a> -->

										<!--
										<div class="gsaplightbox ' . $signature_id . '">

											<div class="close-container" onclick="' . $signature_id . 'hideGSAPBox()"></div>

											<div class="video-container">
												<div class="hide" onclick="' . $signature_id . 'hideGSAPBox()">X</div>
												<video class="myVideo" controls>
													<source src="' . $video_url . '" type="video/mp4">
													Your browser does not support the video tag.
												</video>
												<br>
												<br>
												<a href="' . $stellenanzeigen_btn_url . '" class="btn">' . $stellenanzeigen_btn_title . '</a>
											</div>
										</div>
										--> 
										';

			$output .=		   	'<script> 

										function ' . $signature_id .'fireGSAPVideoBox() {

											console.log("SignatureID: '. $signature_id . '")

											let signatureID = "'. $signature_id . '"
											let stellenanzeigenParameter = "'. $stellenanzeigen_parameter . '"
											let mainTextarea = "' . $main_textarea . '"
											let videoURL = "' . $video_url . '"
											let webmVideoURL = "' . $webm_video_url . '"
											let firstLink = "' . $stellenanzeigen_btn_url . '"
											let fistLinkText = "' . $stellenanzeigen_btn_title . '"
											let ankerlinkCheckbox = "' . $ankerlink_checkbox . '"
											let ankerlinkParameter = "' . $ankerlink_parameter . '"
											
											showVideo(signatureID, stellenanzeigenParameter, mainTextarea, videoURL, webmVideoURL, firstLink, fistLinkText, ankerlinkCheckbox, ankerlinkParameter)

										}


										/*********************
										 *** OLD FUNCTIONS ***
										 *********************/

										// Get an element"s distance from the top of the page
										/*
										const getElemDistance = (elem) => {
											var location = 0;
											if (elem.offsetParent) {
												do {
													location += elem.offsetTop;
													elem = elem.offsetParent;
												} while (elem);
											}
											return location >= 0 ? location : 0;
										};
										*/
										let ' . $signature_id . 'Elem = document.querySelector(".' . $signature_id . '");
										console.log("' . $signature_id . 'Elem: ", ' . $signature_id . 'Elem)

										// get Video to start when box open 
										let ' . $signature_id . 'vid = document.querySelector(".' . $signature_id .' .myVideo");

										function ' . $signature_id . 'fireGSAPBox() {
											console.log("fireGSAPBox fired")

											// Get current location"s distance from the top of the page
											let position = window.pageYOffset;

											// Get new Location
											let location = getElemDistance(' . $signature_id . 'Elem);
											console.log("location: ", location)

											// Get parent container | getParentContainer
											let parentContainer = getParentContainer(' . $signature_id . 'Elem)
											console.log("parentContainer: ", parentContainer)


											/********/
											/* GSAP */ 
											/********/

											// GSAP bring container to front 
											gsap.to("#" + parentContainer, {duration: 0, zIndex: "999" });
											
											// GSAP Animation Lightbox
											gsap.to(".' . $signature_id .'", {duration: 1, y: - location, height: "100%", position: "fixed", overflow: "auto", zIndex: "99" });
											// GSAP Animation Header 
											gsap.to("#header-outer", {duration: 0, zIndex: "11", opacity: "0.66" });
											// GSAP Animation Footer 
											gsap.to("#footer-outer", {duration: 0, zIndex: "1" });

											
											// Play Video 
											' . $signature_id . 'vid.play();

											// Set Event Listener when vid endet 
											' . $signature_id . 'vid.addEventListener("ended", (event) => {
												console.log("Video stopped either because 1) it was over, " +
												"or 2) no further data is available.");
											});
										}

										// Hide Box again 
										function ' . $signature_id . 'hideGSAPBox() {
											console.log("GSAP HIDE")

											// Get parent container | getParentContainer
											let parentContainer = getParentContainer(' . $signature_id . 'Elem)
											console.log("parentContainer: ", parentContainer)

											// GSAP bring container z-index to normal 
											gsap.to("#" + parentContainer, {duration: 0, zIndex: "1" });

											// GSAP Animation Lightbox
											gsap.to(".' . $signature_id . '", {duration: 0, y: location, height: "0px", position: "relative", overflow: "hidden", padding: "0%", zIndex: "1" });

											// GSAP Animation Header 
											gsap.to("#header-outer", {duration: 1, zIndex: "999" });
											// GSAP Animation Footer 
											gsap.to("#footer-outer", {duration: 1, zIndex: "10" });

											// Play Video 
											' . $signature_id . 'vid.pause();
										}



										</script>


										<style>
										.gsaplightbox {
											background: rgba(0,0,0,0.33);
											padding: 0%;
											text-align: center;
											overflow: hidden;
											height: 0px;
											transform: translate(0px, 300px);
											/* position: fixed; */
											width: 100%;
											/* margin-left: calc((1245px - 100%) / 2); */
											display: flex;
											align-items: center;
										}

										.close-container {
											position: absolute;
											z-index: 0;
											width: 100%;
											height: 100vh;
										}

										.gsaplightbox .video-container {
											width: auto;
											text-align: center;
											margin: auto;
										}

										.gsaplightbox video {
											/* margin-left: calc((1245px - 100%) / 2);  */
											max-height: 60vh;
											max-width: 100%;
										}

										div#header-outer {
											transition: opacity 240ms ease;
										}
										div#header-outer:hover {
											opacity: 1 !important;
										}
										</style>
										';
											

			$output .=					'<!-- </div> <!-- End content-width -->

									<!-- </div> End fullwidth -->

								</div> <!-- End berding-video-lightbox -->
								';



			$output .= '</div>'; // End .berding-video-lightbox-modul HTML

			return $output;

		}
	}
	new BerdingVideoLightbox();

}






/* SOFTGARDEN API MODULE */ 
if ( ! class_exists( 'BerdingSoftgardenModule' ) ) {

	class BerdingSoftgardenModule extends WPBakeryShortCode {

		function __construct() {
			add_action( 'init', array( $this, 'create_shortcode' ), 999 );
			add_shortcode( 'berding_softgarden_module', array( $this, 'render_shortcode' ) );
		}

		public function create_shortcode() {
			// Stop all if VC is not enabled
			if ( !defined( 'WPB_VC_VERSION' ) ) {
				return;
			}

			// Map blockquote with vc_map()
			vc_map( array(
				'name'          => __('Berding Softgarden Module', 'rr'),
				'base'          => 'berding_softgarden_module',
				'description'  	=> __( '', 'rr' ),
				'category'      => __( 'rr Modules', 'rr'),
				'params' => array(

					// array(
					// 	"type" => "textarea_html",
					// 	"holder" => "div",
					// 	"class" => "",
					// 	"heading" => __( "Blockquote Content", 'rr' ),
					// 	"param_name" => "content", // Important: Only one textarea_html param per content element allowed and it should have "content" as a "param_name"
					// 	"value" => __( "I am test text block. Click edit button to change this text.", 'rr' ),
					// 	"description" => __( "Enter content.", 'rr' )
					// ),

					 
					array(
						"type" => "textarea",
						"holder" => "div",
						"class" => "",
						"heading" => __( "Blockquote Content", 'rr' ),
						"param_name" => "main_textarea", // Important: Only one textarea_html param per content element allowed and it should have "content" as a "param_name"
						"value" => __( "<p>I am test text block. Click edit button to change this text.</p>", 'rr' ),
						"description" => __( "Enter content.", 'rr' )
					),
					

					array( 
						'type' => 'dropdown', 
						'heading' => __( 'Default Bereich', "rr" ), 
						'param_name' => 'default_bereich', 
						'value' => array( 
									__( 'Alle', "Alle Bereiche" ) => 'allareas', 
									__( 'Produktion', "sam42_manualwork" ) => 'sam42_manualwork', 
									__( 'Technik', "sam42_engineering" ) => 'sam42_engineering',
									__( 'Vertrieb', "sam42_sales" ) => 'sam42_sales',
									__( 'Verwaltung', "sam42_administration" ) => 'sam42_administration', ), 
									"description" => __( "Hier wird der Bereich vorausgewählt", "Hier wird der Bereich vorausgewählt" )
					),

				
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Default Textsuche', 'rr' ),
						'param_name'    => 'text_one',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Auf dieser Seite voreingestellte Textsuche (Kann geändert werden)', 'rr' ),
					),

					array(
						'type'          => 'textfield',
						'heading'       => __( 'Placeholder Textsuche', 'rr' ),
						'param_name'    => 'placeholder_one',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Falls keine "Default Textsuche" angegeben wurde, wird der Placeholder angezeigt, der hier angegeben werden kann', 'rr' ),
					),

					/*
					array(
						'type'          => 'textfield',
						'holder'        => 'div',
						'heading'       => __( 'Author Jobs', 'rr' ),
						'param_name'    => 'text_two',
						'value'         => __( '', 'rr' ),
						'description'   => __( 'Add Author Quote.', 'rr' ),
					),
					*/

					array(
						"type" => "vc_link",
						"class" => "",
						"heading" => __( "Link", 'rr' ),
						"param_name" => "job_link",
						"description" => __( "Nutzen wir diesen?", 'rr' ),
					),

					/* Testimonial Image */ 
					array(
						"type" => "attach_image",
						"class" => "",
						'holder'        => 'div',
						"heading" => __( "Testimonial Image URL", "rr" ),
						"param_name" => "image_source",
						"value" => '',
						"description" => __( "Testimonial Image", "rr" ),
					),

					array(
						'type'          => 'textfield',
						'heading'       => __( 'Element ID', 'rr' ),
						'param_name'    => 'element_id',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Enter element ID (Note: make sure it is unique and valid).', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),

					array(
						'type'          => 'textfield',
						'heading'       => __( 'Extra class name', 'rr' ),
						'param_name'    => 'extra_class',
						'value'             => __( '', 'rr' ),
						'description'   => __( 'Style particular content element differently - add a class name and refer to it in custom CSS.', 'rr' ),
						'group'         => __( 'Extra', 'rr'),
					),
				),
			));

		}


		public function render_shortcode( $atts ) {
			$atts = (shortcode_atts(array(
				'main_textarea'		=> '',
				'job_link'  		=> '',
				'text_one'      	=> '',
				'placeholder_one' 	=> '',
				'text_two' 			=> '',
				'default_bereich' 	=> '',
				'image_source' 		=> '',

				'extra_class'       => '',
				'element_id'        => '',

			), $atts));
			
			// ///////////////////////////////////////////// //
			// get the json response from the softgarden api // 
			// ///////////////////////////////////////////// //
			function fetchJobList() {
				$url = 'https://api.softgarden.io/api/rest/v3/frontend/jobslist/79593_extern';
				$headers = array(
					'Content-Type: application/json',
					'Authorization: Basic ' . base64_encode($_ENV['API_KEY_Softgarden'] . ':')
					// 'Authorization: Basic ' . base64_encode('XXX-XXX-XXX-XXX:')
				);
			
				$curl = curl_init($url);
				curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
				curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
				$response = curl_exec($curl);
			
				if ($response === FALSE) {
					$curlErrorCode = curl_errno($curl); // Get the cURL error code
					$curlErrorMessage = curl_error($curl); // Get the cURL error message
			
					// Throw an exception with custom error message based on cURL error code
					switch ($curlErrorCode) {
						case CURLE_COULDNT_RESOLVE_HOST:
							throw new Exception('Failed to fetch job list: Could not resolve host');
						case CURLE_OPERATION_TIMEOUTED:
							throw new Exception('Failed to fetch job list: Operation timeout');
						default:
							throw new Exception('Failed to fetch job list: ' . $curlErrorMessage);
					}
				}
			
				curl_close($curl);
			
				return $response;
			}
			
			try {
				// Call the fetchJobList function to get the JSON response
				$jsonResponse = fetchJobList();
			
				// Echo the JSON response as JavaScript variable
				// echo '<script type="module" >export const jobData = ' . $jsonResponse . ';</script>';
				echo '<script type="module" >window.jobData = ' . $jsonResponse . ';</script>';
			} catch (Exception $e) {
				// Handle the exception
				echo 'Error: ' . $e->getMessage();
			}

			//Content
			// $content     = wpb_js_remove_wpautop($content, true);
			$text_one       		= esc_html($atts['text_one']);
			$placeholder_one       	= esc_html($atts['placeholder_one']);
			$text_two       		= esc_html($atts['text_two']);

			// Default Bereich 
			$default_bereich = esc_html($atts['default_bereich']);

			// main_textarea
			$main_textarea 		= esc_html($atts['main_textarea']);

			//SOFTGARDEN API Link (weiß noch gar nicht ob ich sie brauche)
			$job_link  = vc_build_link( $atts['job_link'] );
			$job_title   = esc_html($job_link["title"]);
			$job_url     = esc_url( $job_link['url'] );

			// Author/Testimonial Image 
			$image_source = esc_html($atts['image_source']);

			// Job Kategorien 
			$sam42_manualwork = 'jobCategorie';

			//Class and Id
			$extra_class        = esc_attr($atts['extra_class']);
			$element_id         = esc_attr($atts['element_id']);

			////////////
			// OUTPUT //
			///////////

			
			
			// $output = '<script>const jobDataFromPHP = ' . fetchJobList() . ';</script>';
			
			// $output .= '<script type="text/javascript" src="http://maps.google.com/maps/api/js?sensor=false"></script>';
			// $output .= '<script type="text/javascript" src="https://www.googleapis.com/geolocation/v1/geolocate?key=AIzaSyBfbHtV1A-Sd-rqittQKZNsdVpQgdGX4LA"></script>';
			
			// Das war das funktionierende 
			// $output .= '<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAeb6kp6yo0iWN19KKUlagy7vy37p3XjxE"></script>';

			$output .= /*html*/"

					<script type='module' src='/wp-content/themes/salient-child/softgarden-app.js'> </script>

					<link rel='stylesheet' href='/wp-content/themes/salient-child/stellenanzeigen.css'>

					";

			$output .= '<div class="softgarden-modul">';


			$output .= '<p class="main-content hide-it">' . $main_textarea . '</p>';
			$output .= '<p class="default-textsuche hide-it">'. $text_one .'</p>';
			$output .= '<p class="default-bereich hide-it">'. $default_bereich .'</p>';
			$output .= '<p class="default-bereich hide-it">'. $placeholder_one .'</p>';

				/*
				$output .= '<script> 
								if ("' . $default_bereich . '" == "produktion") {
									jobCategorie == sam42_manualwork
									console.log("default_bereich == produktion: also jobCategorie == ", jobCategorie)
								} else {
									console.log("default_bereich != produktion also passiert nichts")
								}
							</script>';
				*/
			
				$output .= '<div id="app">';
					// $output .= '<div class="vignetten-wrapper new-style"></div>';


						$output .= /*html*/'
											<div class="softgarden-controls background">
												<div class="content-width">
													
													<div class="main-search"> 

														<div class="top-row">

															<div class="search-wrapper textsuche test ' . $placeholder_one . '">
																';
																
						if($placeholder_one) {
							$output .=						'<input class="searchinput" v-model="textsuche" @keyup.enter="searchForLetters()" placeholder=" ' . $placeholder_one . ' ">';
						} else {
							$output .=						'<input class="searchinput" v-model="textsuche" @keyup.enter="searchForLetters()" placeholder="z.B. Gabelstaplerfahrer">';
						}
																
						$output .= /*html*/'			</div>

															<!-- <p>currentAdressAvailable: {{ currentAdressAvailable }} </p> -->

															<div class="search-wrapper adress">
																<span class="hinweis-deine-adresse" v-show="useNavigatorAdress && adressInput.length == 0">Dein aktueller Standort</span> 
																<div class="adress-wrapper">
																	<button class="yourAdress" v-if="!useNavigatorAdress && currentAdressAvailable == true" @click="useNavigatorAdressFunction()">Deine Adresse benutzen</button> 
																	<input class="searchinput" v-model="adressInput" @keyup.enter="getSearchedGeoDistance(nutzeEingabe)" placeholder="Postleitzahl oder Stadt">
																</div>
															</div>


															<div class="distance-wrapper"> 
																<button @click="openChangeDistance = true"> 
																	{{ maxDistance }} <span v-show="!isNaN(maxDistance)">km</span>
																	<span class="material-symbols-outlined">
																		expand_more
																	</span>
																</button>

																<div class="change-distance" v-show="openChangeDistance" v-on:mouseleave="openChangeDistance = false">
																	<button @click="setMaxDistanceto(25)" :class="{active: maxDistance == 25}">
																		25km
																	</button>
																	<button @click="setMaxDistanceto(50)" :class="{active: maxDistance == 50}">
																		50km
																	</button>
																	<!--
																	<button @click="setMaxDistanceto(75)" :class="{active: maxDistance == 75}">
																		75km
																	</button>
																	-->
																	<button @click="setMaxDistanceto(100)" :class="{active: maxDistance == 100}">
																		100km
																	</button>
																	<button @click="setMaxDistanceto(200)" :class="{active: maxDistance == 200}">
																		200km
																	</button>
																	<button @click="setMaxDistancetoInfinity()">
																		Egal
																	</button>
																</div>
															</div>

															<!-- Location Suche --> 
															<button class="search" @click="getSearchedGeoDistance()">
																<span class="material-symbols-outlined">
																	search
																</span>
															</button> 

															<!-- Textsuche 
															<button class="search" @click="searchForLetters()">
																<span class="material-symbols-outlined">
																	search
																</span>
															</button> 
															--> 
														</div>

							
													</div>

													<div class="category-search">
														<div class="bottom-row">
															<button @click="jobCategorie = all" :class="{active: jobCategorie == all}">Alle</button>
															<button @click="jobCategorie = sam42_manualwork" :class="{active: jobCategorie == sam42_manualwork}">Produktion</button>
															<button @click="jobCategorie = sam42_engineering" :class="{active: jobCategorie == sam42_engineering}">Technik</button>
															<button @click="jobCategorie = sam42_sales" :class="{active: jobCategorie == sam42_sales}">Vertrieb</button>
															<button @click="jobCategorie = sam42_administration" :class="{active: jobCategorie == sam42_administration}">Verwaltung</button>
															<!-- <button @click="jobCategorie = sam42_purchase" :class="{active: jobCategorie == sam42_purchase}">Einkauf</button> -->
															<!-- <button @click="jobCategorie = sam42_it" :class="{active: jobCategorie == sam42_it}">IT</button> -->
														</div>
													</div>

												</div>
											</div>						
											';

						


					$output .= '<div class="content-width">';

						// Optinal Image 
						$output .=  '<!-- Optional Image --> 
									<div class="image-wrapper">
										<!-- <img class="person" src="' . wp_get_attachment_image_url( $image_source, $size = 'full' ) . '" alt=""> --> 
									</div>';

						// $output .= 	'<div class="author">
						// 				<p class="author">' . $text_one . '</p>
						// 				<p class="author">' . $text_two . '</p>
						// 			</div>';

						// $output .= '<a href="' . $job_url . '" class="btn">' . $job_title . '</a>';

						// Left 33 | Descition 
						// $output .= 	'<div class="left33">';



						// Right 66 | Actual Jobs 
						// $output .= '<div class="right66">';
						

						// 100% ROw for Jobs
						$output .= '<div class="full100">';
							
							$output .= 	/*html*/'<!-- Display Jobs by Softgarden API & Vue --> 

												<div v-if="loading" class="job-loader">
													<div class="lds-ring"><div></div><div></div><div></div><div></div></div>	
												</div>

												<!-- Job Header | Job Bezeichnungen – Kategorie – Standort – Distanz -->
												<div v-if="jobData" >

													<div class="job-infos-bar job-header">
															<div class="left50">
																Job Bezeichnung
															</div>

															<!--
															<div class="category">
																Kategorie
															</div>
															-->

															<div class="location">
																Standort
															</div>

															<div class="distance" v-if="submittedAdressInput">
																Distanz zu {{ submittedAdressInput }}
															</div>

															<div class="button-space"></div>
													</div>

													
													

													<ul class="jobs card" v-if="filteredJobData">

														<!-- Suchkriterien führen zu keinen Ergebnissen --> 

														<li v-show="!filteredJobs.length"> 
															<label class="job-header">
																<div class="single-job-wrapper no-job">
																	<div class="job-header">
																	
																		<a href="https://short.sg/j/30241459" target="_blank" class="left-job-flex">

																			<div class="left50">

																				<h3>Leider existieren aktuell keine Stellen, die Ihren Suchkriterien entsprechen. <br>Wir freuen uns dennoch über eine Initiativbewerbung von Ihnen</h3> 
																			</div>
																		
																		</a>

																		<div class="job-btn-wrapper">
																			<div class="inner-wrapper">
																				<a href="https://jobdb.softgarden.de/jobdb/public/jobposting/applyonline/click?jp=30241459&ADP" target="_blank" class="btn">Jetzt bewerben</a>
																			</div>
																		</div>

																	</div>
																</div>
															</label>
														</li>
 
														<li v-for="job in filteredJobs" :key="job.jobDbId">
															<!-- <div class="single-job-wrapper" v-if="job.distanceToCurrentPosition <= maxDistance || maxDistance == null"> --> 
															<!-- <a :href="job.job_ad_url" target="_blank"> -->
															<div class="single-job-wrapper">
																	<div class="job-header">

																		<a :href="job.job_ad_url" target="_blank" class="left-job-flex">

																			<div class="left50">
																				<!-- Ketegorie --> 
																				<div class="category">
																					<p v-if="job.jobCategories == sam42_manualwork || job.jobCategories == produktion4">Produktion</p>
																					<p v-if="job.jobCategories == sam42_engineering || job.jobCategories == technik4">Technik</p>
																					<p v-if="job.jobCategories == sam42_sales">Vertrieb</p>
																					<p v-if="job.jobCategories == sam42_administration || job.jobCategories == sam42_purchase || job.jobCategories == sam42_it || job.jobCategories == verwaltung4">Verwaltung</p>
																				</div>
																				<!-- Jobbezeichnung --> 
																				<h3>
																						{{ job.externalPostingName }}
																				</h3>
																				<!-- Label to expand -->
																				<label :for="job.jobDbId">
																					<span class="material-symbols-outlined" @click="showStellenanzeigenVue(job)">
																						expand_more
																					</span>
																				</label>
																			</div>

																			<div class="location">
																				<p>
																					<span class="material-symbols-outlined">
																						location_on
																					</span>
																					{{ job.geo_city }} <!-- ({{ job.geo_country }}) -->
																				</p>
																				<p v-if="job.distanceToCurrentPosition <= 2000">
																					<span class="material-symbols-outlined">
																						route
																					</span>
																					<span>
																						{{ job.distanceToCurrentPosition }}km
																					</span>
																				</p>
																			</div>

																		</a>

																		<!--
																		<div class="distance" v-if="submittedAdressInput">
																			/
																		</div>
																		-->

																		<!-- <a :href="job.job_ad_url" target="_blank" class="btn">Mehr erfahren</a> -->
																		<!-- <a :href="job.applyOnlineLink" target="_blank" class="btn">Jetzt bewerben</a> -->

																		<!-- <p @click="window.open(job.applyOnlineLink)" class="btn" style="opacity: 0; pointer-events: none;">Jetzt bewerben</p>  --> 

																		<div class="job-btn-wrapper">
																			<div class="inner-wrapper">
																				<a :href="job.applyOnlineLink" target="_blank" class="btn">Jetzt bewerben</a>
																			</div>
																		</div>

																	</div>
																

																

																<!-- Input und Description --> 
																<input type="checkbox" class="chck1" :id="job.jobDbId">
																<div class="description hide">
																		
																	<!-- <div class="html-import" v-html="job.jobAdText"></div> -->
																	<br>
																	<a :href="job.applyOnlineLink" class="btn" target="_blank">Jetzt bewerben</a> 
																</div>
																	
															</div> 
															<!-- </a> -->
														</li>

														<!--
														<li v-for="job in filteredJobs" :key="job.jobDbId">
															<div class="single-job-wrapper">
																<label class="job-header" :for="job.jobDbId">

																	<div class="left50">
																		<a :href="job.job_ad_url" target="_blank">
																			<p>{{ job.externalPostingName }}</p>
																		</a>
																	</div>

																	<div class="category">
																		<p v-if="job.jobCategories == sam42_manualwork">Produktion</p>
																		<p v-if="job.jobCategories == sam42_engineering">Technik</p>
																		<p v-if="job.jobCategories == sam42_sales">Vertrieb</p>
																		<p v-if="job.jobCategories == sam42_administration || sam42_purchase || sam42_it>Verwaltung"></p>
																	</div>

																	<div class="location">
																		<p>{{ job.geo_city }} ({{ job.geo_country }}) </p>
																	</div>

																	<div class="distance" v-if="submittedAdressInput">
																		<p v-if="currentUserLocation.lat">{{ job.distanceToCurrentPosition }}km</p>
																	</div>

																	<a :href="job.job_ad_url" target="_blank" class="btn">Mehr erfahren</a>
																	<a :href="job.applyOnlineLink" target="_blank" class="btn">Jetzt bewerben</a> 

																</label>
																-->
																<!--
																<input type="checkbox" class="chck1" :id="job.jobDbId">
																<div class="description hide">
																	<hr>
																	
																	<div class="html-import" v-html="job.jobAdText"></div> 
																	
																	<a :href="job.applyOnlineLink" target="_blank">jetzt bewerben – {{ job.applyOnlineLink }}</a> 
																</div>
																-->
																<!-- 
															</div> 
														</li>
														-->
													</ul>



													<!--
													<h2> Filtered Jobs </h2>
													<p> filteredJobData: {{ filteredJobData }}</p>
													<p> filteredJobs: {{ filteredJobs }}</p>
													-->
													
													<!-- Hier werden einfach alle Jobs ausgegeben 
													<ul v-for="job in jobData" :key="job.jobDbId">
														<li>
															<h1>{{ job.externalPostingName }}</h1>
															<p>Standort: {{ job.geo_city }} ({{ job.geo_country }})  </p>
															
															<hr>
															
															<div v-html="job.jobAdText"></div>
															
															<a :href="job.applyOnlineLink" target="_blank">jetzt bewerben</a>	
														</li>
													</ul>
													-->
													
												</div>

											</div>
										';

						$output .= '</div>'; // End right66

					$output .= '</div>'; // End Content Width 

				$output .= '</div>'; 

			$output .= '</div>';

			return $output;

		}

	}

	new BerdingSoftgardenModule();

}

?>