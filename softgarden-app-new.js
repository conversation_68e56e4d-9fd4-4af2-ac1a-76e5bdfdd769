// import { createApp, ref, computed, watchEffect } from 'https://unpkg.com/vue@3/dist/vue.esm-browser.js'
import { createApp, ref, computed, watchEffect, onMounted } from '/wp-content/themes/salient-child/vue.esm-browser.js'

createApp({
    setup() {

        // loading var to show and hide loading spinner 
        let loading = ref(true) 

        // Get current User Location 
        const useNavigatorAdress = ref(false) // wird genutzt 
        const currentAdressAvailable = ref(false)

        const currentUserLocation = ref({
            display: '',
            lat: '',
            lon: ''	
        })


        /* Ref to store and maipulate all Jobs */ 
        const jobData = ref(null)

        /* Categories */ 
        const jobCategorie 			= ref('all')

        const all 						= ref('all')
        const sam42_manualwork 			= ref('sam42_manualwork')
        const produktion4 			    = ref('b5dff439-b746-4fb5-b75b-5ce388a6ed4a')
        const sam42_engineering 		= ref('sam42_engineering')
        const technik4 		            = ref('ad262e88-b4db-46f5-8d2f-3bf1111df8bf')
        const sam42_sales 				= ref('sam42_sales')
        const sales4                    = ref('1948893c-56ac-4dc7-a6b1-486c507b8b01') // ID muss noch geändert werden, wenn Job zu der Kategorie in Softgarden angelegt wurde
        const sam42_administration 		= ref('sam42_administration')
        const sam42_purchase 			= ref('sam42_purchase')
        const verwaltung4 			    = ref('1948893c-56ac-4dc7-a6b1-486c507b8b01')
        const sam42_it 					= ref('sam42_it')


        /* Suchfeld Textsuche */ 
        const textsuche = ref('')

        // Check "Bereich" und "Textsuche" if Defined in Backend 
        const getDefaultBereich = () => {
            console.log('FIND BEREICH')
            let bereich = document.querySelector('.default-bereich').innerHTML
            if(bereich.length > 4) {
                jobCategorie.value = bereich
            }

            let phpTextsuche = document.querySelector('.default-textsuche').innerHTML
            textsuche.value = phpTextsuche
        }
        getDefaultBereich()


        // Local Storage for showing in Lightbox
        const checkForLocalStorage = () => {
            if(localStorage.getItem("stellenname") !== null) {
                // console.log('stellenname: ', localStorage.getItem("stellenname"))
                textsuche.value = localStorage.getItem("stellenname")
                // Hier lösche ich den Local Storage direkt wieder 
                localStorage.removeItem('stellenname');
            } else {
                console.log('no local storage "stellenname" exist')
            }
        }
        checkForLocalStorage()
        

        // Capitalize First Letter Function 
        const capitalizeFirstLetter = (string) => {
            if(textsuche.value.length) {
                return string.charAt(0).toUpperCase() + string.slice(1);
            } else {
                console.log('textsuche is empthy so we dont need to capitalize')
            }
            
        }


        const maxDistance = ref('Distanz')

        const openChangeDistance = ref(false)

        const setMaxDistanceto = (userChoosenDistance) => {
            maxDistance.value = userChoosenDistance
            openChangeDistance.value = false 
        }

        const setMaxDistancetoInfinity = () => {
            maxDistance.value = 'Distanz'
            openChangeDistance.value = false 
        }
          
        
        /* COMPARE FUNCTION */ 
        const compare = ( a, b ) => {
            if ( Number(a.distanceToCurrentPosition) < Number(b.distanceToCurrentPosition) ){
              return -1;
            }
            if ( Number(a.distanceToCurrentPosition) > Number(b.distanceToCurrentPosition) ){
              return 1;
            }
            return 0;
        }


        
        /* GET DISTANCE FROM EACH JOB Function | requires GEOAPIFY  */
        function deg2rad(deg) {
            return deg * (Math.PI/180)
        } 
        function getDistanceFromLatLonInKm(lat1,lon1,lat2,lon2) {
            var R = 6371; // Radius of the earth in km
            var dLat = deg2rad(lat2-lat1);  // deg2rad below
            var dLon = deg2rad(lon2-lon1); 
            var a = 
            Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
            Math.sin(dLon/2) * Math.sin(dLon/2)
            ; 
            var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); 
            var d = R * c; // Distance in km
            // console.log('d (distance): ', d)
            return d;
        }

        /* //////// */ 
        /* GEOAPIFY */ 
        /* //////// */ 
        const geoSearchPosition = ref({
            lat: '',
            lon: ''
        })

        const adressInput = ref('');

        const submittedAdressInput = ref();
     
        var requestOptions = {
            method: 'GET',
        };

        const setJobDistanceDependingOnUseNavigatorAdress = () => {
            // console.log('%c setJobDistanceDependingOnUseNavigatorAdress fired!!!', 'color: #D10; font-size: 24px;')
            submittedAdressInput.value = adressInput.value
            if(submittedAdressInput.value) {
                console.log('%c FIRED IF | fetch api ', 'color: #D10; font-size: 24px;')
                fetch(`https://api.geoapify.com/v1/geocode/search?text=${submittedAdressInput.value}&apiKey=efac25b85129482d93b7da1e823920db`, requestOptions)
                .then(response => response.json())
                .then(result => {
                    if (result.features.length) {
                        console.log('RESULTS: result.features[0].properties.lat', result.features[0].properties.lat);
                        geoSearchPosition.value.lat = result.features[0].properties.lat
                        console.log('RESULTS: result.features[0].properties.lon', result.features[0].properties.lon);
                        geoSearchPosition.value.lon = result.features[0].properties.lon

                        /* MANIPULATE JOBS / ADD ORE CHANGE DISTANCE TO POSITION */ 
                        if(jobData.value) {
                            if(geoSearchPosition.value.lat && geoSearchPosition.value.lon) {
                                console.log('FIRED ELSE IF | not navigatorAdress')

                                for (let i = 0; i < jobData.value.length; i++) {
                                    let distanceForCurrentJob = getDistanceFromLatLonInKm(geoSearchPosition.value.lat,geoSearchPosition.value.lon,jobData.value[i].geo_lat,jobData.value[i].geo_long).toFixed(1)

                                    jobData.value[i].distanceToCurrentPosition = distanceForCurrentJob;
                                }
                                jobData.value.sort(compare)
                            }
                        }
                    } else {
                        console.log('No address found');
                    }
                })
            } else if(useNavigatorAdress.value == true && currentUserLocation.value.lat != null) {
                currentAdressAvailable.value = true 

                console.log('%c FIRED IF | navigatorAdress', 'color: #D10; font-size: 24px;')
                console.log('Get Distance fired with navigator values (lat, lon)')
                console.log('navigator values = ', currentUserLocation.value.lat, currentUserLocation.value.lon)

                for (let i = 0; i < jobData.value.length; i++) {

                    let distanceForCurrentJob = getDistanceFromLatLonInKm(currentUserLocation.value.lat,currentUserLocation.value.lon,jobData.value[i].geo_lat,jobData.value[i].geo_long).toFixed(1)

                    jobData.value[i].distanceToCurrentPosition = distanceForCurrentJob;
                }
                jobData.value.sort(compare)

            } else {
                console.log('Anscheinend wurde der Zugriff auf Deinen Standort nicht zugelassen');
            }
        };

        // Function to set Job Distance from Navigator and sort them 
        const setJobDistanceFromNavigator = () => {
            console.log('setJobDistanceFromNavigator fired!')
            if(useNavigatorAdress.value == true && typeof currentUserLocation.value.lat == 'number') {

                console.log('currentUserLocation.value.lat: ', currentUserLocation.value.lat)

                for (let i = 0; i < jobData.value.length; i++) {  
                    let distanceForCurrentJob = getDistanceFromLatLonInKm(currentUserLocation.value.lat,currentUserLocation.value.lon,jobData.value[i].geo_lat,jobData.value[i].geo_long).toFixed(1)
                    jobData.value[i].distanceToCurrentPosition = distanceForCurrentJob;
                }
                jobData.value.sort(compare)
            } else {
                useNavigatorAdress.value = false 
                console.log('Anscheinend hast Du den Zugriff auf Deinen Standort nicht zugelassen');
            }
        }
        

        // Distance Function just to Log 
        const getDistance = async () => {
            // Real Distance 
            console.log('lat & lon from Search Adress to Stockholm: ', geoSearchPosition.value.lat, geoSearchPosition.value.lon, 59.330957,18.075256)
            console.log('Die Distanz ist ', getDistanceFromLatLonInKm(geoSearchPosition.value.lat,geoSearchPosition.value.lon,59.3225525,13.4619422).toFixed(1), 'km');
        }


        /******************************************* */
        /* GET NEW DISTANCE FROM CHANGED SEARCHFIELD */ 
        /******************************************* */

        // Real Distance Function 
        const getSearchedGeoDistance = () => {
            console.log('getSearchedGeoDistance fired!!!');
            console.log('submittedAdressInput.value: ', submittedAdressInput.value );

            useNavigatorAdress.value = false 
            console.log('useNavigatorAdress.value to false?: ', useNavigatorAdress.value)
            
            setJobDistanceDependingOnUseNavigatorAdress()
        }

        const useNavigatorAdressFunction = () => {
            console.log('useNavigatorAdressFunction fired!')
            useNavigatorAdress.value = true
            adressInput.value = ''
            console.log('useNavigatorAdress -> ', useNavigatorAdress.value)
            setJobDistanceFromNavigator()
        }


        /*****************************/
        /* searchForLetters Function */ 
        /*****************************/

        const searchedJobData = ref()
        const searchForLetters = () => {
            if(!textsuche.value == '' && textsuche.value.length > 0) {
                    console.log('searchForLetters -> IF -> textsuche.value.length != 0')
                    console.log('textsuche.value: ', textsuche.value)

                    searchedJobData.value = jobData.value.filter(
                    jobData => jobData.externalPostingName.includes(textsuche.value) || jobData.keywords.includes(textsuche.value)
                );

                        // console.log('searchedJobData.value: ', searchedJobData.value)
            } else {
                        // console.log('searchForLetters -> ELSE -> textsuche.value.length == 0')
                        // console.log('textsuche.value: ', textsuche.value)

                searchedJobData.value = jobData.value

                        // console.log('searchedJobData.value: ', searchedJobData.value)
            }
        }


        /* //////////////////////////// */
        /* HIER MANIPULIERE ICH JobData */
        /* //////////////////////////// */

        const addDistanceToJobs = () => {
            for (let i = 0; i < jobData.value.length; i++) {
                let distanceForCurrentJob = getDistanceFromLatLonInKm(geoSearchPosition.value.lat,geoSearchPosition.value.lon,jobData.value[i].geo_lat,jobData.value[i].geo_long).toFixed(1)
                jobData.value[i].distanceToCurrentPosition = distanceForCurrentJob;
            }
            jobData.value.sort(compare)
            console.log('sorted jobData Value?: ', jobData.value)
            
            console.log('Fire searchForLetters()')
            searchForLetters()
        }


        /* //////////////////////////////////////////////////// */  
        /* GET THE JOBS FROM SOFTGARDEN API AND MANIPULATE THEM */ 
        /* //////////////////////////////////////////////////// */ 

        const fetchJobList = async () => {
            // const Response = await fetch('https://api.softgarden.io/api/rest/v3/frontend/jobslist/79593_extern', {
            //     Method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //         'Authorization': 'Basic ' + btoa('XXX-XXX-XXX-XXX:')
            //     }
            // })
            
            // const res = await Response.json();
            // console.log(res);
            jobData.value = window.jobData.results
            loading.value = false 
            
            addDistanceToJobs()

        }


        /* /////////////////////////////////////// */
        /* Get Distance from navigator.geolocation */
        /* must be under "fetchJobs" initialisation */
        /* /////////////////////////////////////// */

        /* showPosition for setting lat and long of current Position */ 
        const showPosition = (position) => {
            console.log('📍 User Location: ', position)
            console.log('Latitude: ' + position.coords.latitude + ' | Longitude: ' + position.coords.longitude)
            // currentUserLocation.value.display = 
            currentUserLocation.value.lat = position.coords.latitude
            currentUserLocation.value.lon = position.coords.longitude

            currentAdressAvailable.value = true 
        }

        /* Get Actual Location */ 
        const getLocation = () => {
            if (navigator.geolocation) {
                console.log('W3 geolocation: ', navigator.geolocation)
                navigator.geolocation.getCurrentPosition(showPosition);
                fetchJobList()
            } else { 
                console.log('Geolocation is not supported by this browser.');
                currentUserLocation.value.lat = null
                currentUserLocation.value.lon = null
            }
        }
        // getLocation()   





        // computed ref for filtered Jobs 
        const filteredJobs = ref();


        const filteredJobData = computed(() => {
            ///////////////////////////////////////////////////////////////////
            // Wenn jobData existiert und die Kategorie 'all' ausgewählt ist //
            ///////////////////////////////////////////////////////////////////
            if(jobData.value != null && jobCategorie.value == 'all') {

                // CHeck ob TEXTSUCHE leer ist 
                if(textsuche.value == '') {
                    filteredJobs.value = jobData.value.filter(jobData => jobData.jobCategories);
                } else {
                    //////////////////////////////////////////////////////////////////////////////////////////////
                    //		 									!!! WICHTIG !!!									//
                    // Es ist wichtig nach allem zu suchen – auch nach den Worten in Klein- oder Großbuchstaben //
                    //																							//
                    //////////////////////////////////////////////////////////////////////////////////////////////
                    let textsucheTrim = textsuche.value.trim()
                    let textsucheToLowerCase = textsuche.value.toLowerCase()
                    let keywordsTrim = textsuche.value.trim()
                    let keywordstoLowerCase = textsuche.value.toLowerCase()
                    let textsucheCapitalized = capitalizeFirstLetter(textsuche.value)

                    filteredJobs.value = jobData.value.filter(
                        filteredJobs => 	// Jobbezeichnung  
                                            filteredJobs.externalPostingName.includes(textsucheTrim) || 
                                            filteredJobs.externalPostingName.includes(textsucheToLowerCase.trim()) ||
                                            filteredJobs.externalPostingName.includes(textsucheCapitalized.trim()) ||
                                            // Keywords 
                                            filteredJobs.keywords.includes(keywordsTrim) ||
                                            filteredJobs.keywords.includes(keywordstoLowerCase.trim()) ||
                                            filteredJobs.keywords.includes(textsucheCapitalized.trim()) 
                    );
                }

                if(maxDistance != 'Distanz') {
                    filteredJobs.value = filteredJobs.value.filter(filteredJobs => filteredJobs.distanceToCurrentPosition <= maxDistance.value);
                } else {
                    filteredJobs.value = filteredJobs.value.filter(filteredJobs => filteredJobs.jobCategories);
                }

                // return jobData.value.filter(jobData => jobData.jobCategories);
                console.log('filteredJobs all ausgeführt', filteredJobs.value)
                console.log('filteredJobData all ausgeführt', filteredJobData)
                return filteredJobs

            ///////////////////////////////////////////////////////////////////////////////
            // Wenn jobData existiert und eine ANDERE Kategorie als 'all' ausgewählt ist //
            ///////////////////////////////////////////////////////////////////////////////
            } else if(jobData.value != null && jobCategorie.value != 'all') {
                // filteredJobs.value = jobData.value.filter(jobData => jobData.jobCategories == jobCategorie.value);
                
                // CHeck ob TEXTSUCHE leer ist 
                if(textsuche.value == '') {
                    filteredJobs.value = jobData.value.filter(jobData => jobData.jobCategories);
                } else {
                    //////////////////////////////////////////////////////////////////////////////////////////////
                    //		 									!!! WICHTIG !!!									//
                    // Es ist wichtig nach allem zu suchen – auch nach den Worten in Klein- oder Großbuchstaben //
                    //																							//
                    //////////////////////////////////////////////////////////////////////////////////////////////
                    let textsucheTrim = textsuche.value.trim()
                    let textsucheToLowerCase = textsuche.value.toLowerCase()
                    let keywordsTrim = textsuche.value.trim()
                    let keywordstoLowerCase = textsuche.value.toLowerCase()
                    let textsucheCapitalized = capitalizeFirstLetter(textsuche.value)

                    filteredJobs.value = jobData.value.filter(
                        filteredJobs => 	// Jobbezeichnung  
                                            filteredJobs.externalPostingName.includes(textsucheTrim) || 
                                            filteredJobs.externalPostingName.includes(textsucheToLowerCase.trim()) ||
                                            filteredJobs.externalPostingName.includes(textsucheCapitalized.trim()) ||
                                            // Keywords 
                                            filteredJobs.keywords.includes(keywordsTrim) ||
                                            filteredJobs.keywords.includes(keywordstoLowerCase.trim()) ||
                                            filteredJobs.keywords.includes(textsucheCapitalized.trim()) 
                    );
                }

                if(maxDistance.value != 'Distanz') {
                    filteredJobs.value = filteredJobs.value.filter(filteredJobs => filteredJobs.jobCategories == jobCategorie.value);
                    filteredJobs.value = filteredJobs.value.filter(filteredJobs => filteredJobs.distanceToCurrentPosition <= maxDistance.value);
                    console.log('filteredJobs (nicht all) mit', jobCategorie.value, ' ausgeführt', filteredJobs.value)
                } else {
                    /* Check if "sam42_administration" is choosen */ 
                    if(jobCategorie.value == sam42_administration.value) {
                        filteredJobs.value = filteredJobs.value.filter(filteredJobs => filteredJobs.jobCategories == jobCategorie.value || filteredJobs.jobCategories == sam42_purchase.value || filteredJobs.jobCategories == verwaltung4.value);
                    } else if(jobCategorie.value == sam42_engineering.value) {
                        filteredJobs.value = filteredJobs.value.filter(filteredJobs => filteredJobs.jobCategories == jobCategorie.value || filteredJobs.jobCategories == sam42_engineering.value || filteredJobs.jobCategories == technik4.value);
                    } else if(jobCategorie.value == sam42_manualwork.value) {
                        filteredJobs.value = filteredJobs.value.filter(filteredJobs => filteredJobs.jobCategories == jobCategorie.value || filteredJobs.jobCategories == sam42_manualwork.value || filteredJobs.jobCategories == produktion4.value);
                    } else {
                    filteredJobs.value = filteredJobs.value.filter(filteredJobs => filteredJobs.jobCategories == jobCategorie.value);
                    // console.log('filteredJobs (nicht all) mit', jobCategorie.value, ' ausgeführt', filteredJobs.value)
                    }
                }
                // return jobData.value.filter(jobData => jobData.jobCategories == jobCategorie.value);
                return filteredJobs
             } else {
                console.log('jobData is null', jobData.value)
            }
        }) 

        const logFilteredJobs = () => {
            console.log('filteredJobs.value', filteredJobs.value)
        }
        logFilteredJobs()

        const showStellenanzeigenVue = (job) => {
            console.log('showStellenanzeigenVue fired', job)
            console.log('job.applyOnlineLink', job.applyOnlineLink)
            showStellenanzeigen(job)
        }
            
        

        return {
            // loading 
            loading, 

            // Current Position  
            currentUserLocation,
            currentAdressAvailable,
            useNavigatorAdress, 
            useNavigatorAdressFunction,
            currentUserLocation,
            showPosition,

            getLocation,

            // Job Search Functions
            searchForLetters, 		 
            textsuche, 					
            searchedJobData, 			// output for test purpose 

            getDistance, 				
            getSearchedGeoDistance, 	
            adressInput, 			
            submittedAdressInput,

            // maxDistance 
            maxDistance,
            openChangeDistance, 
            setMaxDistanceto,
            setMaxDistancetoInfinity,

            // JobData = Alle Jobs als Object 
            jobData,

            // Job Kategorien 
            jobCategorie,

            all,
            sam42_manualwork,
            produktion4,
            sam42_engineering,
            technik4,
            sam42_sales,
            sam42_administration,
            verwaltung4,
            sam42_purchase,
            sam42_it,

            // log filtered jobs 
            logFilteredJobs,


            // filteredJobs 
            filteredJobData,
            filteredJobs,

            // Function to show single stellenanzeige in Lightbox
            showStellenanzeigenVue
        }
    }
}).mount('#app')